/* Contact Section Component Styles */

.contactSection {
  padding: 120px 0;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 25%, #334155 50%, #1e293b 75%, #0f172a 100%);
  position: relative;
  overflow: hidden;
  min-height: 100vh;
}

.contactSection::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(147, 51, 234, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 50% 50%, rgba(16, 185, 129, 0.1) 0%, transparent 70%);
  pointer-events: none;
  animation: backgroundPulse 25s ease-in-out infinite;
}

@keyframes backgroundPulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 20px;
  position: relative;
  z-index: 2;
}

/* Hero Section */
.heroSection {
  text-align: center;
  margin-bottom: 80px;
}

.heroContent {
  max-width: 800px;
  margin: 0 auto;
  text-align: center;
}

.badge {
  display: inline-block;
  background: linear-gradient(135deg, #3b82f6, #2563eb, #1d4ed8);
  color: white;
  padding: 12px 28px;
  border-radius: 30px;
  font-size: 0.9rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 2px;
  margin-bottom: 25px;
  box-shadow:
    0 8px 25px rgba(59, 130, 246, 0.4),
    0 4px 15px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.mainTitle {
  font-size: 4rem;
  font-weight: 900;
  background: linear-gradient(135deg, #ffffff 0%, #e2e8f0 20%, #3b82f6 40%, #60a5fa 60%, #ffffff 80%, #f1f5f9 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-bottom: 30px;
  line-height: 1.1;
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  animation: titleGlow 4s ease-in-out infinite;
}

@keyframes titleEntrance {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes titleGlow {
  0%, 100% {
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  }
  50% {
    text-shadow:
      0 4px 8px rgba(0, 0, 0, 0.3),
      0 0 30px rgba(59, 130, 246, 0.4),
      0 0 60px rgba(59, 130, 246, 0.2);
  }
}

.heroDescription {
  font-size: 1.3rem;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.6;
  margin: 0;
  animation: descriptionEntrance 1s ease-out 0.2s both;
}

@keyframes descriptionEntrance {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Main Content */
.mainContent {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 60px;
  margin-bottom: 100px;
  align-items: start;
}

/* Form Section */
.formSection {
  animation: formEntrance 1s ease-out 0.4s both;
}

@keyframes formEntrance {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.formContainer {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  border-radius: 24px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(30px);
  padding: 40px;
  box-shadow: 
    0 20px 50px rgba(0, 0, 0, 0.3),
    0 10px 30px rgba(59, 130, 246, 0.1);
}

.contactForm {
  display: flex;
  flex-direction: column;
  gap: 25px;
}

.formGrid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 25px;
}

.inputGroup {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.label {
  font-size: 1rem;
  font-weight: 600;
  color: #e2e8f0;
  margin-bottom: 5px;
}

.input,
.textarea {
  padding: 16px 20px;
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.05);
  color: white;
  font-size: 1rem;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.input::placeholder,
.textarea::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.input:focus,
.textarea:focus {
  outline: none;
  border-color: #3b82f6;
  background: rgba(255, 255, 255, 0.08);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.textarea {
  resize: vertical;
  min-height: 120px;
  font-family: inherit;
}

.submitButton {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 18px 35px;
  background: linear-gradient(135deg, #3b82f6, #2563eb, #1d4ed8);
  color: white;
  border: none;
  border-radius: 50px;
  font-weight: 700;
  font-size: 1.1rem;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 
    0 12px 30px rgba(59, 130, 246, 0.4),
    0 6px 20px rgba(0, 0, 0, 0.2);
  position: relative;
  overflow: hidden;
  margin-top: 10px;
}

.submitButton::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.6s;
}

.submitButton:hover::before {
  left: 100%;
}

.submitButton:hover {
  transform: translateY(-2px);
  box-shadow: 
    0 20px 40px rgba(59, 130, 246, 0.5),
    0 10px 30px rgba(0, 0, 0, 0.3);
}

.submitButton:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.submitting {
  background: linear-gradient(135deg, #6b7280, #4b5563, #374151);
}

.successMessage,
.errorMessage {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 15px 20px;
  border-radius: 12px;
  font-weight: 600;
  margin-top: 15px;
}

.successMessage {
  background: rgba(16, 185, 129, 0.1);
  border: 1px solid rgba(16, 185, 129, 0.3);
  color: #10b981;
}

.errorMessage {
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.3);
  color: #ef4444;
}

/* Info Section */
.infoSection {
  display: flex;
  flex-direction: column;
  gap: 25px;
  animation: infoEntrance 1s ease-out 0.6s both;
}

@keyframes infoEntrance {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.officeCard,
.contactCard,
.socialCard {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.08), rgba(255, 255, 255, 0.03));
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  padding: 30px;
  transition: all 0.3s ease;
}

.officeCard:hover,
.contactCard:hover,
.socialCard:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
  border-color: rgba(59, 130, 246, 0.3);
}

.officeHeader {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 15px;
}

.officeHeader i {
  font-size: 1.5rem;
  color: #3b82f6;
}

.officeTitle,
.cardTitle {
  font-size: 1.3rem;
  font-weight: 700;
  background: linear-gradient(135deg, #ffffff 0%, #e2e8f0 30%, #3b82f6 70%, #ffffff 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 12px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.cardTitle i {
  font-size: 1.2rem;
  color: #3b82f6;
}

.address {
  font-style: normal;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
  margin: 0;
}

.contactLink {
  color: #60a5fa;
  text-decoration: none;
  font-weight: 600;
  font-size: 1.1rem;
  transition: color 0.3s ease;
}

.contactLink:hover {
  color: #3b82f6;
}

.socialLinks {
  display: flex;
  gap: 15px;
  margin-top: 15px;
}

.socialLink {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 45px;
  height: 45px;
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  border-radius: 50%;
  font-size: 1.3rem;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.socialLink:hover {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
}

/* Offices Section */
.officesSection {
  margin-bottom: 80px;
  animation: officesEntrance 1s ease-out 0.8s both;
}

@keyframes officesEntrance {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.sectionTitle {
  font-size: 3rem;
  font-weight: 900;
  text-align: center;
  background: linear-gradient(135deg, #ffffff 0%, #e2e8f0 20%, #3b82f6 40%, #60a5fa 60%, #ffffff 80%, #f1f5f9 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-bottom: 50px;
  line-height: 1.1;
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  animation: titleGlow 4s ease-in-out infinite;
}

.officesGrid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 40px;
  max-width: 1000px;
  margin: 0 auto;
}

.officeItem {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(30px);
  padding: 35px;
  display: flex;
  gap: 20px;
  transition: all 0.3s ease;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.officeItem:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 50px rgba(0, 0, 0, 0.3);
  border-color: rgba(59, 130, 246, 0.3);
}

.officeFlag {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  border-radius: 50%;
  color: white;
  font-size: 1.5rem;
  flex-shrink: 0;
}

.officeDetails {
  flex: 1;
}

.officeLocation {
  font-size: 1.4rem;
  font-weight: 700;
  background: linear-gradient(135deg, #ffffff 0%, #e2e8f0 30%, #3b82f6 70%, #ffffff 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin: 0 0 10px 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.officeAddress {
  font-style: normal;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
  margin: 0 0 15px 0;
}

.officePhone {
  color: #60a5fa;
  text-decoration: none;
  font-weight: 600;
  font-size: 1.1rem;
  transition: color 0.3s ease;
}

.officePhone:hover {
  color: #3b82f6;
}

/* CTA Section */
.ctaSection {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(147, 51, 234, 0.1));
  border-radius: 30px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(30px);
  padding: 60px 40px;
  text-align: center;
  animation: ctaEntrance 1s ease-out 1s both;
}

@keyframes ctaEntrance {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.ctaContent {
  max-width: 600px;
  margin: 0 auto;
}

.ctaTitle {
  font-size: 2.5rem;
  font-weight: 900;
  background: linear-gradient(135deg, #ffffff 0%, #60a5fa 30%, #3b82f6 60%, #ffffff 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-bottom: 20px;
  line-height: 1.2;
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  animation: titleGlow 4s ease-in-out infinite;
}

.ctaDescription {
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.6;
  margin-bottom: 35px;
}

.ctaButton {
  display: inline-flex;
  align-items: center;
  gap: 15px;
  padding: 20px 40px;
  background: linear-gradient(135deg, #10b981, #059669, #047857);
  color: white;
  text-decoration: none;
  border-radius: 50px;
  font-weight: 700;
  font-size: 1.2rem;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 
    0 12px 30px rgba(16, 185, 129, 0.4),
    0 6px 20px rgba(0, 0, 0, 0.2);
  position: relative;
  overflow: hidden;
}

.ctaButton::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.6s;
}

.ctaButton:hover::before {
  left: 100%;
}

.ctaButton:hover {
  transform: translateY(-3px);
  box-shadow:
    0 20px 40px rgba(16, 185, 129, 0.5),
    0 10px 30px rgba(0, 0, 0, 0.3);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .mainContent {
    grid-template-columns: 1fr;
    gap: 50px;
  }

  .officesGrid {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .formGrid {
    grid-template-columns: 1fr;
    gap: 20px;
  }
}

@media (max-width: 768px) {
  .contactSection {
    padding: 80px 0;
  }

  .container {
    padding: 0 15px;
  }

  .mainTitle {
    font-size: 2.8rem;
  }

  .heroDescription {
    font-size: 1.1rem;
  }

  .formContainer {
    padding: 30px 25px;
  }

  .sectionTitle {
    font-size: 2.2rem;
  }

  .ctaTitle {
    font-size: 2rem;
  }

  .ctaDescription {
    font-size: 1.1rem;
  }

  .officeItem {
    padding: 25px;
    flex-direction: column;
    text-align: center;
  }

  .officeFlag {
    align-self: center;
  }
}

@media (max-width: 640px) {
  .contactSection {
    padding: 60px 0;
  }

  .container {
    padding: 0 10px;
  }

  .mainTitle {
    font-size: 2.2rem;
  }

  .heroDescription {
    font-size: 1rem;
  }

  .formContainer {
    padding: 25px 20px;
  }

  .input,
  .textarea {
    padding: 14px 16px;
    font-size: 0.95rem;
  }

  .submitButton {
    padding: 16px 30px;
    font-size: 1rem;
  }

  .sectionTitle {
    font-size: 1.8rem;
  }

  .ctaSection {
    padding: 40px 25px;
  }

  .ctaTitle {
    font-size: 1.6rem;
  }

  .ctaDescription {
    font-size: 1rem;
  }

  .ctaButton {
    padding: 16px 30px;
    font-size: 1rem;
  }

  .socialLinks {
    justify-content: center;
  }

  .officeItem {
    padding: 20px;
  }

  .officeFlag {
    width: 50px;
    height: 50px;
    font-size: 1.2rem;
  }

  .officeLocation {
    font-size: 1.2rem;
  }
}

@media (max-width: 480px) {
  .mainTitle {
    font-size: 1.8rem;
  }

  .heroDescription {
    font-size: 0.95rem;
  }

  .formContainer {
    padding: 20px 15px;
  }

  .contactForm {
    gap: 20px;
  }

  .inputGroup {
    gap: 6px;
  }

  .label {
    font-size: 0.9rem;
  }

  .input,
  .textarea {
    padding: 12px 14px;
    font-size: 0.9rem;
  }

  .submitButton {
    padding: 14px 25px;
    font-size: 0.95rem;
  }

  .officeCard,
  .contactCard,
  .socialCard {
    padding: 20px;
  }

  .cardTitle,
  .officeTitle {
    font-size: 1.1rem;
  }

  .sectionTitle {
    font-size: 1.5rem;
  }

  .ctaTitle {
    font-size: 1.4rem;
  }

  .ctaDescription {
    font-size: 0.95rem;
  }

  .ctaButton {
    padding: 14px 25px;
    font-size: 0.95rem;
  }
}
