/* Services Section Component Styles */

.servicesSection {
  padding: 0;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #f1f5f9 100%);
  position: relative;
  overflow: hidden;
}

.servicesSection::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(147, 51, 234, 0.1) 0%, transparent 50%);
  pointer-events: none;
}

.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 20px;
  position: relative;
  z-index: 2;
}

/* Header Section */
.headerSection {
  text-align: center;
  margin-bottom: 80px;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.mainTitle {
  font-size: 3.5rem;
  font-weight: 900;
  background: linear-gradient(135deg, #1e293b 0%, #3b82f6 50%, #1e293b 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-bottom: 30px;
  line-height: 1.1;
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.description {
  font-size: 1.2rem;
  color: #64748b;
  line-height: 1.8;
  margin-bottom: 40px;
  text-align: center;
}

.actionButtons {
  display: flex;
  gap: 20px;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
}

.primaryButton {
  display: inline-flex;
  align-items: center;
  gap: 12px;
  padding: 16px 32px;
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  color: white;
  text-decoration: none;
  border-radius: 50px;
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 
    0 10px 25px rgba(59, 130, 246, 0.3),
    0 4px 12px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.primaryButton::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.primaryButton:hover::before {
  left: 100%;
}

.primaryButton:hover {
  transform: translateY(-2px);
  box-shadow: 
    0 15px 35px rgba(59, 130, 246, 0.4),
    0 8px 20px rgba(0, 0, 0, 0.15);
}

.secondaryButton {
  display: inline-flex;
  align-items: center;
  gap: 12px;
  padding: 16px 32px;
  background: rgba(255, 255, 255, 0.9);
  color: #3b82f6;
  text-decoration: none;
  border-radius: 50px;
  font-weight: 600;
  font-size: 1rem;
  border: 2px solid #3b82f6;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
}

.secondaryButton:hover {
  background: #3b82f6;
  color: white;
  transform: translateY(-2px);
  box-shadow: 
    0 10px 25px rgba(59, 130, 246, 0.3),
    0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Highlights Section */
.highlightsSection {
  margin-top: 60px;
}

.highlightsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 30px;
  max-width: 1200px;
  margin: 0 auto;
}

.highlightCard {
  background: rgba(255, 255, 255, 0.9);
  padding: 40px 30px;
  border-radius: 20px;
  text-align: center;
  position: relative;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid rgba(59, 130, 246, 0.1);
  backdrop-filter: blur(20px);
  box-shadow: 
    0 10px 30px rgba(0, 0, 0, 0.1),
    0 4px 15px rgba(59, 130, 246, 0.1);
}

.highlightCard:hover {
  transform: translateY(-8px);
  box-shadow: 
    0 20px 50px rgba(0, 0, 0, 0.15),
    0 10px 30px rgba(59, 130, 246, 0.2);
  border-color: rgba(59, 130, 246, 0.3);
}

.highlightIcon {
  width: 70px;
  height: 70px;
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 25px;
  color: white;
  font-size: 1.8rem;
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
  transition: all 0.3s ease;
}

.highlightCard:hover .highlightIcon {
  transform: scale(1.1);
  box-shadow: 0 12px 35px rgba(59, 130, 246, 0.4);
}

.highlightNumber {
  font-size: 2.8rem;
  font-weight: 800;
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  line-height: 1;
  margin-bottom: 10px;
}

.highlightLabel {
  font-size: 1.1rem;
  color: #1e293b;
  font-weight: 600;
  margin-bottom: 8px;
}

.highlightDescription {
  font-size: 0.95rem;
  color: #64748b;
}

.highlightGlow {
  position: absolute;
  top: -50%;
  left: -50%;
  right: -50%;
  bottom: -50%;
  background: radial-gradient(circle, rgba(59, 130, 246, 0.1) 0%, transparent 70%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.highlightCard:hover .highlightGlow {
  opacity: 1;
}

/* Animations */
.fadeInUp {
  animation: fadeInUp 0.8s ease-out;
}

.slideInUp {
  animation: slideInUp 0.6s ease-out var(--delay, 0s) both;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(40px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .servicesSection {
    padding: 0 0 60px 0;
  }

  .container {
    padding: 0 15px;
  }

  .headerSection {
    margin-bottom: 50px;
  }

  .mainTitle {
    font-size: 2.5rem;
    line-height: 1.2;
  }

  .description {
    font-size: 1.1rem;
    margin-bottom: 30px;
  }

  .actionButtons {
    flex-direction: column;
    gap: 15px;
  }

  .primaryButton,
  .secondaryButton {
    width: 100%;
    max-width: 300px;
    justify-content: center;
  }

  .highlightsGrid {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }

  .highlightCard {
    padding: 30px 20px;
  }

  .highlightIcon {
    width: 60px;
    height: 60px;
    font-size: 1.5rem;
    margin-bottom: 20px;
  }

  .highlightNumber {
    font-size: 2.2rem;
  }

  .highlightLabel {
    font-size: 1rem;
  }

  .highlightDescription {
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .servicesSection {
    padding: 0 0 40px 0;
  }

  .mainTitle {
    font-size: 2rem;
  }

  .description {
    font-size: 1rem;
  }

  .highlightsGrid {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .highlightCard {
    padding: 25px 15px;
  }

  .highlightNumber {
    font-size: 1.8rem;
  }
}
