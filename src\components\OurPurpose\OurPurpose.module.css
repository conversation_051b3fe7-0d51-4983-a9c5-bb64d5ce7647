/* Our Purpose Component Styles */

.purposeSection {
  padding: 80px 0;
  background: linear-gradient(135deg, #0c0a1e 0%, #1a0f2e 25%, #2d1065 50%, #1e0a3e 75%, #0f0a1c 100%);
  position: relative;
  overflow: hidden;
  min-height: 100vh;
}

/* Enhanced Background Effects */
.backgroundEffects {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
  pointer-events: none;
}

/* Hexagonal Grid Pattern */
.hexagonalGrid {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0.1;
}

.hexagon {
  position: absolute;
  width: 60px;
  height: 60px;
  background: linear-gradient(45deg, rgba(29, 78, 216, 0.3), rgba(59, 130, 246, 0.2));
  clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%);
  animation: hexagonFloat 15s ease-in-out infinite;
}

.hexagon:nth-child(1) { top: 10%; left: 5%; animation-delay: 0s; }
.hexagon:nth-child(2) { top: 15%; left: 25%; animation-delay: -2s; }
.hexagon:nth-child(3) { top: 20%; left: 45%; animation-delay: -4s; }
.hexagon:nth-child(4) { top: 25%; left: 65%; animation-delay: -6s; }
.hexagon:nth-child(5) { top: 30%; left: 85%; animation-delay: -8s; }
.hexagon:nth-child(6) { top: 50%; left: 15%; animation-delay: -10s; }
.hexagon:nth-child(7) { top: 55%; left: 35%; animation-delay: -12s; }
.hexagon:nth-child(8) { top: 60%; left: 55%; animation-delay: -14s; }
.hexagon:nth-child(9) { top: 65%; left: 75%; animation-delay: -16s; }
.hexagon:nth-child(10) { top: 70%; left: 95%; animation-delay: -18s; }
.hexagon:nth-child(11) { top: 80%; left: 10%; animation-delay: -20s; }
.hexagon:nth-child(12) { top: 85%; left: 30%; animation-delay: -22s; }

/* Flowing Lines */
.flowingLines {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.line1,
.line2,
.line3 {
  position: absolute;
  height: 2px;
  background: linear-gradient(90deg, transparent, rgba(29, 78, 216, 0.6), transparent);
  animation: flowLine 12s linear infinite;
}

.line1 {
  top: 20%;
  width: 100%;
  animation-delay: 0s;
}

.line2 {
  top: 50%;
  width: 100%;
  animation-delay: -4s;
}

.line3 {
  top: 80%;
  width: 100%;
  animation-delay: -8s;
}

/* Gradient Orbs */
.gradientOrbs {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.gradientOrb {
  position: absolute;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(29, 78, 216, 0.4) 0%, rgba(59, 130, 246, 0.2) 50%, transparent 100%);
  animation: orbFloat 20s ease-in-out infinite;
}

.gradientOrb:nth-child(1) { width: 120px; height: 120px; top: 5%; left: 10%; animation-delay: 0s; }
.gradientOrb:nth-child(2) { width: 80px; height: 80px; top: 15%; right: 15%; animation-delay: -3s; }
.gradientOrb:nth-child(3) { width: 100px; height: 100px; top: 40%; left: 5%; animation-delay: -6s; }
.gradientOrb:nth-child(4) { width: 90px; height: 90px; top: 60%; right: 10%; animation-delay: -9s; }
.gradientOrb:nth-child(5) { width: 70px; height: 70px; bottom: 20%; left: 20%; animation-delay: -12s; }
.gradientOrb:nth-child(6) { width: 110px; height: 110px; bottom: 10%; right: 25%; animation-delay: -15s; }
.gradientOrb:nth-child(7) { width: 60px; height: 60px; top: 30%; left: 50%; animation-delay: -18s; }
.gradientOrb:nth-child(8) { width: 85px; height: 85px; bottom: 40%; right: 40%; animation-delay: -21s; }

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 15px;
  position: relative;
  z-index: 2;
}

/* Section Header */
.sectionHeader {
  text-align: center;
  margin-bottom: 80px;
  animation: fadeInUp 1s ease-out;
}

.headerBadge {
  display: inline-flex;
  align-items: center;
  gap: 12px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.08));
  color: #3b82f6;
  padding: 12px 30px;
  border-radius: 30px;
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 25px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
  text-transform: uppercase;
  letter-spacing: 1px;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.headerBadge i {
  font-size: 1.2rem;
  animation: targetPulse 3s ease-in-out infinite;
  color: #f59e0b;
}

.sectionTitle {
  font-size: 4rem;
  font-weight: 800;
  background: linear-gradient(135deg, #ffffff 0%, #e2e8f0 30%, #ffffff 60%, #f1f5f9 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  line-height: 1.2;
  margin-bottom: 30px;
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.4);
}

.titleHighlight {
  background: linear-gradient(135deg, #60a5fa 0%, #3b82f6 50%, #2563eb 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.sectionDescription {
  font-size: 1.3rem;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.8;
  max-width: 900px;
  margin: 0 auto;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Purpose Grid */
.purposeGrid {
  display: flex;
  flex-direction: column;
  gap: 100px;
  position: relative;
  margin-bottom: 40px;
  max-width: 1400px;
  margin-left: auto;
  margin-right: auto;
  padding: 0 60px;
}

.purposeCard {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.08));
  padding: 50px 40px;
  border-radius: 30px;
  backdrop-filter: blur(30px);
  border: 2px solid rgba(255, 255, 255, 0.1);
  box-shadow:
    0 30px 100px rgba(0, 0, 0, 0.5),
    0 15px 50px rgba(29, 78, 216, 0.3),
    inset 0 2px 0 rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  animation: cardEntrance 1s ease-out 0.3s both;
  width: 75%;
  position: relative;
}

/* Alternating positions for cards */
.purposeCard:nth-child(1) {
  align-self: flex-start;
  margin-left: -80px;
  margin-right: auto;
}

.purposeCard:nth-child(2) {
  align-self: flex-end;
  margin-right: -80px;
  margin-left: auto;
}

/* Removed dotted connection lines for cleaner design */

/* Removed dotted line animations */

/* Removed connection dots for cleaner design */

/* Dynamic Decorative Animations */
@keyframes decorativePulse {
  0%, 100% {
    opacity: 0.6;
    transform: translateX(-50%) scaleY(1);
    filter: blur(0px);
  }
  50% {
    opacity: 1;
    transform: translateX(-50%) scaleY(1.1);
    filter: blur(0.5px);
  }
}

@keyframes floatingOrb {
  0%, 100% {
    transform: translateX(-50%) translateY(0px) rotate(0deg);
    opacity: 0.3;
  }
  33% {
    transform: translateX(-50%) translateY(-10px) rotate(120deg);
    opacity: 0.6;
  }
  66% {
    transform: translateX(-50%) translateY(5px) rotate(240deg);
    opacity: 0.4;
  }
}

@keyframes decorativeLineFlow {
  0%, 100% {
    opacity: 0.7;
    transform: translateY(-50%) rotate(var(--rotation, 15deg)) scaleX(1);
  }
  50% {
    opacity: 1;
    transform: translateY(-50%) rotate(var(--rotation, 15deg)) scaleX(1.2);
  }
}

@keyframes decorativeDot {
  0%, 100% {
    opacity: 0.8;
    transform: translateY(-50%) scale(1);
  }
  50% {
    opacity: 1;
    transform: translateY(-50%) scale(1.3);
  }
}

/* Mixed Style Animations */
@keyframes geometricFloat {
  0%, 100% {
    transform: rotate(45deg) translateY(0px);
    opacity: 0.6;
  }
  50% {
    transform: rotate(45deg) translateY(-8px);
    opacity: 1;
  }
}

@keyframes geometricPulse {
  0%, 100% {
    transform: rotate(90deg) scale(1);
    opacity: 0.5;
  }
  50% {
    transform: rotate(90deg) scale(1.2);
    opacity: 0.8;
  }
}

@keyframes lineStretch {
  0%, 100% {
    transform: scaleX(1);
    opacity: 0.6;
  }
  50% {
    transform: scaleX(1.3);
    opacity: 1;
  }
}

.purposeCard::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 5px;
  background: linear-gradient(90deg, #60a5fa, #3b82f6, #2563eb);
  box-shadow: 0 0 25px rgba(59, 130, 246, 0.5);
}

.purposeCard:hover {
  transform: translateY(-15px) scale(1.03);
  box-shadow:
    0 40px 120px rgba(29, 78, 216, 0.4),
    0 20px 60px rgba(0, 0, 0, 0.4),
    inset 0 3px 0 rgba(255, 255, 255, 0.3);
  border-color: rgba(59, 130, 246, 0.3);
}

.purposeCard:nth-child(1):hover {
  transform: translateY(-15px) scale(1.03) translateX(15px);
}

.purposeCard:nth-child(2):hover {
  transform: translateY(-15px) scale(1.03) translateX(-15px);
}

.cardHeader {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 25px;
}

.cardIcon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 2rem;
  box-shadow:
    0 15px 40px rgba(59, 130, 246, 0.4),
    0 5px 15px rgba(0, 0, 0, 0.3);
  animation: iconGlow 3s ease-in-out infinite;
}

.cardTitle {
  font-size: 2.2rem;
  font-weight: 700;
  color: white;
  margin: 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.cardDescription {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.7;
  margin-bottom: 35px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* Steps List */
.stepsList {
  display: flex;
  flex-direction: column;
  gap: 30px;
  position: relative;
}

/* Dynamic Decorative Lines System */
.stepsList::before {
  content: '';
  position: absolute;
  left: 50%;
  top: 10%;
  bottom: 10%;
  width: 2px;
  background: linear-gradient(
    180deg,
    transparent 0%,
    rgba(59, 130, 246, 0.3) 20%,
    rgba(37, 99, 235, 0.6) 50%,
    rgba(29, 78, 216, 0.4) 80%,
    transparent 100%
  );
  transform: translateX(-50%);
  border-radius: 2px;
  z-index: 1;
  animation: decorativePulse 4s ease-in-out infinite;
}

/* Curved decorative elements */
.stepsList::after {
  content: '';
  position: absolute;
  left: 50%;
  top: 15%;
  bottom: 15%;
  width: 60px;
  height: 60px;
  border: 2px solid rgba(59, 130, 246, 0.2);
  border-radius: 50%;
  transform: translateX(-50%);
  z-index: 0;
  animation: floatingOrb 6s ease-in-out infinite;
}

.stepItem {
  /* Adaptive CSS Custom Properties */
  --decorative-line-width: 40px;
  --decorative-dot-size: 8px;
  --decorative-rotation: 15deg;
  --decorative-opacity: 0.8;
  --decorative-scale: 1;

  display: flex;
  align-items: center;
  gap: 25px;
  padding: 25px 30px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.08));
  border-radius: 20px;
  border: 2px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(30px);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  animation: stepSlideIn 0.8s ease-out var(--delay) both;
  position: relative;
  width: 55%;
  box-shadow:
    0 15px 40px rgba(0, 0, 0, 0.3),
    0 5px 15px rgba(59, 130, 246, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

/* Alternating positions */
.stepItem:nth-child(odd) {
  align-self: flex-start;
  margin-left: 0;
}

.stepItem:nth-child(even) {
  align-self: flex-end;
  margin-right: 0;
  flex-direction: row-reverse;
}

/* Dynamic Decorative Elements for Step Items */
.stepItem::before {
  content: '';
  position: absolute;
  width: var(--decorative-line-width);
  height: 3px;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(59, 130, 246, var(--decorative-opacity)) 50%,
    transparent 100%
  );
  border-radius: 2px;
  z-index: 2;
  animation: decorativeLineFlow 3s ease-in-out infinite;
  transform-origin: center;
}

.stepItem::after {
  content: '';
  position: absolute;
  width: var(--decorative-dot-size);
  height: var(--decorative-dot-size);
  background: radial-gradient(circle, #3b82f6 0%, #2563eb 70%, transparent 100%);
  border-radius: 50%;
  z-index: 3;
  animation: decorativeDot 2s ease-in-out infinite;
  transform: translateY(-50%) scale(var(--decorative-scale));
}

/* Curved lines for odd items (right side) */
.stepItem:nth-child(odd) {
  --decorative-rotation: 15deg;
}

.stepItem:nth-child(odd)::before {
  right: -45px;
  top: 50%;
  transform: translateY(-50%) rotate(var(--decorative-rotation));
  transform-origin: left center;
}

.stepItem:nth-child(odd)::after {
  right: -25px;
  top: 50%;
  transform: translateY(-50%) scale(var(--decorative-scale));
}

/* Curved lines for even items (left side) */
.stepItem:nth-child(even) {
  --decorative-rotation: -15deg;
}

.stepItem:nth-child(even)::before {
  left: -45px;
  top: 50%;
  transform: translateY(-50%) rotate(var(--decorative-rotation));
  transform-origin: right center;
}

.stepItem:nth-child(even)::after {
  left: -25px;
  top: 50%;
  transform: translateY(-50%) scale(var(--decorative-scale));
}

/* Enhanced Hover Effects with Decorative Elements */
.stepItem:hover {
  /* Card transformations */
  transform: scale(1.03);
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.12));
  border-color: rgba(59, 130, 246, 0.4);
  box-shadow:
    0 25px 60px rgba(59, 130, 246, 0.3),
    0 10px 25px rgba(0, 0, 0, 0.2),
    inset 0 2px 0 rgba(255, 255, 255, 0.3);

  /* Dynamic decorative adaptations */
  --decorative-line-width: calc(var(--decorative-line-width) * 1.3);
  --decorative-dot-size: calc(var(--decorative-dot-size) * 1.4);
  --decorative-scale: calc(var(--decorative-scale) * 1.2);
  --decorative-opacity: 1;
}

.stepItem:hover::before {
  animation-duration: 1.5s;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(96, 165, 250, 0.9) 30%,
    rgba(59, 130, 246, 1) 50%,
    rgba(37, 99, 235, 0.9) 70%,
    transparent 100%
  );
  box-shadow: 0 0 15px rgba(59, 130, 246, 0.6);
}

.stepItem:hover::after {
  animation-duration: 1s;
  background: radial-gradient(circle, #60a5fa 0%, #3b82f6 50%, #2563eb 100%);
  box-shadow: 0 0 10px rgba(96, 165, 250, 0.8);
}

.stepItem:nth-child(odd):hover {
  transform: scale(1.03) translateX(10px);
}

.stepItem:nth-child(odd):hover::before {
  --rotation: 25deg;
}

.stepItem:nth-child(even):hover {
  transform: scale(1.03) translateX(-10px);
}

.stepItem:nth-child(even):hover::before {
  --rotation: -25deg;
}

/* Mixed Style Decorative Elements - Curves and Straight Segments */
.stepsList {
  position: relative;
}

/* Additional curved decorative elements */
.stepItem:nth-child(1) {
  position: relative;
}

.stepItem:nth-child(1)::after {
  content: '';
  position: absolute;
  right: -60px;
  top: 30%;
  width: 30px;
  height: 30px;
  border: 2px solid rgba(96, 165, 250, 0.4);
  border-radius: 50% 0 50% 0;
  transform: rotate(45deg);
  animation: geometricFloat 5s ease-in-out infinite;
}

.stepItem:nth-child(2)::after {
  content: '';
  position: absolute;
  left: -60px;
  top: 30%;
  width: 25px;
  height: 25px;
  border: 2px solid rgba(37, 99, 235, 0.4);
  border-radius: 0 50% 0 50%;
  transform: rotate(-45deg);
  animation: geometricFloat 5s ease-in-out infinite 2.5s;
}

.stepItem:nth-child(3)::after {
  content: '';
  position: absolute;
  right: -55px;
  top: 35%;
  width: 20px;
  height: 20px;
  background: linear-gradient(45deg, transparent 40%, rgba(29, 78, 216, 0.3) 50%, transparent 60%);
  transform: rotate(90deg);
  animation: geometricPulse 4s ease-in-out infinite 1s;
}

.stepItem:nth-child(4)::after {
  content: '';
  position: absolute;
  left: -55px;
  top: 35%;
  width: 28px;
  height: 3px;
  background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.6), transparent);
  border-radius: 2px;
  animation: lineStretch 3s ease-in-out infinite 1.5s;
}

.stepNumber {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 700;
  font-size: 1.3rem;
  flex-shrink: 0;
  box-shadow:
    0 8px 20px rgba(59, 130, 246, 0.4),
    0 3px 8px rgba(0, 0, 0, 0.3);
  border: 2px solid rgba(255, 255, 255, 0.2);
  position: relative;
  z-index: 2;
}

.stepText {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.95);
  font-weight: 500;
  line-height: 1.6;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  position: relative;
  z-index: 2;
}



/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes hexagonFloat {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.3;
  }
  25% {
    transform: translateY(-20px) rotate(90deg);
    opacity: 0.6;
  }
  50% {
    transform: translateY(-10px) rotate(180deg);
    opacity: 0.4;
  }
  75% {
    transform: translateY(-30px) rotate(270deg);
    opacity: 0.7;
  }
}

@keyframes flowLine {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    transform: translateX(100%);
    opacity: 0;
  }
}

@keyframes orbFloat {
  0%, 100% {
    transform: translateY(0px) translateX(0px) scale(1);
    opacity: 0.4;
  }
  25% {
    transform: translateY(-25px) translateX(15px) scale(1.1);
    opacity: 0.7;
  }
  50% {
    transform: translateY(-10px) translateX(-10px) scale(0.9);
    opacity: 0.5;
  }
  75% {
    transform: translateY(-35px) translateX(20px) scale(1.05);
    opacity: 0.8;
  }
}

@keyframes targetPulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
}

@keyframes cardEntrance {
  from {
    opacity: 0;
    transform: translateY(50px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes iconGlow {
  0%, 100% {
    box-shadow:
      0 15px 40px rgba(59, 130, 246, 0.4),
      0 5px 15px rgba(0, 0, 0, 0.3);
  }
  50% {
    box-shadow:
      0 20px 50px rgba(59, 130, 246, 0.6),
      0 8px 20px rgba(0, 0, 0, 0.3);
  }
}

@keyframes stepSlideIn {
  from {
    opacity: 0;
    transform: translateY(50px) scale(0.8);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}



/* Responsive Design */
@media (max-width: 768px) {
  .purposeSection {
    padding: 50px 0;
    min-height: auto;
  }

  .sectionTitle {
    font-size: 2.8rem;
  }

  .sectionDescription {
    font-size: 1.1rem;
  }

  .purposeGrid {
    gap: 40px;
    padding: 0 20px;
  }

  .purposeCard {
    padding: 35px 25px;
    width: 100% !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
  }

  .purposeCard:nth-child(1):hover,
  .purposeCard:nth-child(2):hover {
    transform: translateY(-10px) scale(1.02);
  }

  .cardTitle {
    font-size: 1.8rem;
  }

  .cardIcon {
    width: 60px;
    height: 60px;
    font-size: 1.5rem;
  }

  /* Mobile decorative elements - simplified and repositioned */
  .stepsList::before {
    left: 20px;
    width: 1px;
    transform: none;
    background: linear-gradient(
      180deg,
      transparent 0%,
      rgba(59, 130, 246, 0.4) 30%,
      rgba(37, 99, 235, 0.6) 70%,
      transparent 100%
    );
  }

  .stepsList::after {
    display: none; /* Hide floating orb on mobile */
  }

  .stepItem {
    /* Mobile adaptive properties */
    --decorative-line-width: 20px;
    --decorative-dot-size: 6px;
    --decorative-rotation: 0deg;
    --decorative-scale: 0.8;
    --decorative-opacity: 0.6;

    width: 100%;
    margin-left: 45px !important;
    margin-right: 0 !important;
    flex-direction: row !important;
  }

  /* Simplified decorative lines for mobile */
  .stepItem:nth-child(odd)::before,
  .stepItem:nth-child(even)::before {
    left: -30px;
    right: auto;
    transform: translateY(-50%) rotate(var(--decorative-rotation)) !important;
    background: linear-gradient(90deg, rgba(59, 130, 246, var(--decorative-opacity)), transparent);
  }

  .stepItem:nth-child(odd)::after,
  .stepItem:nth-child(even)::after {
    left: -15px;
    right: auto;
  }

  /* Hide complex decorative elements on mobile */
  .stepItem:nth-child(1)::after,
  .stepItem:nth-child(2)::after,
  .stepItem:nth-child(3)::after,
  .stepItem:nth-child(4)::after {
    display: none;
  }

  .stepItem:nth-child(odd):hover,
  .stepItem:nth-child(even):hover {
    transform: scale(1.02) translateX(5px);
  }

  .stepItem:hover::before {
    transform: translateY(-50%) rotate(0deg) scaleX(1.3) !important;
  }



  .hexagonalGrid,
  .flowingLines,
  .gradientOrbs {
    display: none;
  }
}

/* Tablet responsive design */
@media (max-width: 1024px) and (min-width: 769px) {
  .purposeSection {
    padding: 60px 0;
  }

  .sectionTitle {
    font-size: 3rem;
  }

  .purposeGrid {
    gap: 60px;
    padding: 0 40px;
  }

  .purposeCard {
    padding: 40px 30px;
  }

  .cardTitle {
    font-size: 2rem;
  }

  .cardIcon {
    width: 70px;
    height: 70px;
    font-size: 1.8rem;
  }

  /* Tablet decorative adjustments using custom properties */
  .stepItem {
    --decorative-line-width: 35px;
    --decorative-dot-size: 7px;
    --decorative-scale: 0.9;
    --decorative-opacity: 0.7;
  }

  .stepsList::after {
    width: 50px;
    height: 50px;
  }

  .stepItem:nth-child(1)::after,
  .stepItem:nth-child(2)::after {
    width: 25px;
    height: 25px;
  }

  .stepItem:nth-child(3)::after,
  .stepItem:nth-child(4)::after {
    width: 18px;
  }
}

/* Small mobile devices */
@media (max-width: 480px) {
  .purposeSection {
    padding: 40px 0;
  }

  .sectionTitle {
    font-size: 2.2rem;
    line-height: 1.3;
  }

  .sectionDescription {
    font-size: 1rem;
    padding: 0 10px;
  }

  .purposeGrid {
    gap: 30px;
    padding: 0 15px;
  }

  .purposeCard {
    padding: 25px 20px;
  }

  .cardTitle {
    font-size: 1.6rem;
  }

  .cardDescription {
    font-size: 1rem;
  }

  .cardIcon {
    width: 50px;
    height: 50px;
    font-size: 1.3rem;
  }

  .stepItem {
    /* Extra small mobile adaptive properties */
    --decorative-line-width: 15px;
    --decorative-dot-size: 5px;
    --decorative-scale: 0.7;
    --decorative-opacity: 0.5;

    margin-left: 35px !important;
  }

  .stepsList::before {
    left: 15px;
    width: 1px;
  }

  /* Extra small mobile decorative adjustments */
  .stepItem:nth-child(odd)::before,
  .stepItem:nth-child(even)::before {
    left: -25px;
  }

  .stepItem:nth-child(odd)::after,
  .stepItem:nth-child(even)::after {
    left: -12px;
  }
}
