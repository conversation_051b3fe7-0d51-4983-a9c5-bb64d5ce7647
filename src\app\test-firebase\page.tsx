import { getBlogPosts, getBlogPost } from '@/lib/firestore';
import { db } from '@/lib/firebase';

export default async function TestFirebasePage() {
  console.log('=== Firebase Test Page ===');
  console.log('Database object:', db);
  console.log('Database type:', typeof db);
  console.log('Database null check:', db === null);
  console.log('Database undefined check:', db === undefined);

  let blogPosts = [];
  let error = null;

  try {
    console.log('Attempting to fetch blog posts...');
    blogPosts = await getBlogPosts();
    console.log('Blog posts fetched successfully:', blogPosts.length);
  } catch (err) {
    console.error('Error in test page:', err);
    error = err;
  }

  return (
    <div style={{ padding: '20px', fontFamily: 'monospace' }}>
      <h1>Firebase Connection Test</h1>
      
      <div style={{ marginBottom: '20px' }}>
        <h2>Database Status</h2>
        <p>Database object: {db ? 'Available' : 'Not available'}</p>
        <p>Database type: {typeof db}</p>
        <p>Database null: {String(db === null)}</p>
        <p>Database undefined: {String(db === undefined)}</p>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h2>Blog Posts Fetch Result</h2>
        <p>Posts count: {blogPosts.length}</p>
        <p>Error: {error ? String(error) : 'None'}</p>
      </div>

      <div>
        <h2>Blog Posts Data</h2>
        <pre style={{ background: '#f5f5f5', padding: '10px', overflow: 'auto' }}>
          {JSON.stringify(blogPosts, null, 2)}
        </pre>
      </div>

      {error && (
        <div>
          <h2>Error Details</h2>
          <pre style={{ background: '#ffe6e6', padding: '10px', overflow: 'auto' }}>
            {JSON.stringify(error, null, 2)}
          </pre>
        </div>
      )}
    </div>
  );
}
