/* Featured USA Universities Section */
.featuredSection {
  position: relative;
  padding: 6rem 0;
  background: linear-gradient(135deg, #1e293b 0%, #0f172a 50%, #1e293b 100%);
  overflow: hidden;
}

/* Background Elements */
.backgroundElements {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.bgShape1,
.bgShape2,
.bgShape3 {
  position: absolute;
  border-radius: 50%;
  opacity: 0.08;
  animation: float 10s ease-in-out infinite;
}

.bgShape1 {
  width: 500px;
  height: 500px;
  background: linear-gradient(45deg, #1e3a8a, #dc2626);
  top: -15%;
  left: -10%;
  animation-delay: 0s;
}

.bgShape2 {
  width: 350px;
  height: 350px;
  background: linear-gradient(45deg, #dc2626, #1e3a8a);
  bottom: -10%;
  right: -5%;
  animation-delay: 4s;
}

.bgShape3 {
  width: 250px;
  height: 250px;
  background: linear-gradient(45deg, #1e3a8a, #dc2626);
  top: 40%;
  right: 20%;
  animation-delay: 8s;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-40px) rotate(180deg); }
}

/* Container */
.container {
  position: relative;
  z-index: 10;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 1rem;
}

/* Section Header */
.sectionHeader {
  text-align: center;
  margin-bottom: 4rem;
}

.sectionTitle {
  font-size: 3rem;
  font-weight: 900;
  background: linear-gradient(90deg, #1e3a8a 0%, #dc2626 50%, #1e3a8a 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  color: #dc2626;
  margin-bottom: 1rem;
  line-height: 1.2;
  filter: drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.3));
}

@media (max-width: 768px) {
  .sectionTitle {
    font-size: 2.2rem;
  }
}

.sectionDescription {
  font-size: 1.2rem;
  color: #cbd5e1;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

/* Universities Grid */
.universitiesGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

@media (max-width: 480px) {
  .universitiesGrid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
}

/* University Card */
.universityCard {
  position: relative;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 20px;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.universityCard:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 50px rgba(30, 58, 138, 0.2);
  border-color: rgba(30, 58, 138, 0.3);
}

/* University Badge */
.universityBadge {
  position: absolute;
  top: 1rem;
  right: 1rem;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  z-index: 10;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.gold {
  background: linear-gradient(135deg, #fbbf24, #f59e0b);
  color: #1f2937;
}

.blue {
  background: linear-gradient(135deg, #3b82f6, #1e40af);
  color: white;
}

.red {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
}

.purple {
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
  color: white;
}

.green {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
}

.orange {
  background: linear-gradient(135deg, #f97316, #ea580c);
  color: white;
}

/* University Image */
.universityImage {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.universityImage img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.4s ease;
}

.universityCard:hover .universityImage img {
  transform: scale(1.1);
}

.imageOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to bottom, transparent 0%, rgba(0, 0, 0, 0.3) 100%);
}

/* University Content */
.universityContent {
  padding: 1.5rem;
}

.universityName {
  font-size: 1.4rem;
  font-weight: 700;
  color: #f1f5f9;
  margin-bottom: 0.5rem;
  line-height: 1.3;
}

.universityLocation {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #94a3b8;
  font-size: 0.9rem;
  margin-bottom: 1rem;
}

.universityLocation i {
  color: #dc2626;
}

/* University Stats */
.universityStats {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
}

.statItem {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 0.9rem;
  color: #cbd5e1;
}

.statItem i {
  width: 16px;
  color: #1e3a8a;
  font-size: 0.8rem;
}

/* Learn More Button */
.learnMoreBtn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: linear-gradient(135deg, #1e3a8a, #dc2626);
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 25px;
  text-decoration: none;
  font-weight: 600;
  font-size: 0.9rem;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(30, 58, 138, 0.3);
}

.learnMoreBtn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(30, 58, 138, 0.4);
}

.learnMoreBtn i {
  transition: transform 0.3s ease;
}

.learnMoreBtn:hover i {
  transform: translateX(3px);
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fadeInUp {
  animation: fadeInUp 0.8s ease-out both;
}

.slideInUp {
  animation: slideInUp 0.8s ease-out both;
  animation-delay: var(--delay, 0s);
}

/* Responsive Design */
@media (max-width: 768px) {
  .featuredSection {
    padding: 4rem 0;
  }
  
  .universitiesGrid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .universityCard {
    max-width: 400px;
    margin: 0 auto;
  }
}
