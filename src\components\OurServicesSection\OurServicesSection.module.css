/* Our Services Section Component Styles */

.ourServicesSection {
  padding: 120px 0;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 25%, #334155 50%, #1e293b 75%, #0f172a 100%);
  position: relative;
  overflow: hidden;
}

.ourServicesSection::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(147, 51, 234, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 50% 50%, rgba(16, 185, 129, 0.1) 0%, transparent 70%);
  pointer-events: none;
}

.ourServicesSection::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    linear-gradient(45deg, transparent 30%, rgba(59, 130, 246, 0.03) 50%, transparent 70%),
    linear-gradient(-45deg, transparent 30%, rgba(147, 51, 234, 0.03) 50%, transparent 70%);
  pointer-events: none;
  animation: backgroundShift 20s ease-in-out infinite;
}

.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 20px;
  position: relative;
  z-index: 2;
}

/* Header Section */
.headerSection {
  text-align: center;
  margin-bottom: 80px;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.badge {
  display: inline-block;
  background: linear-gradient(135deg, #3b82f6, #2563eb, #1d4ed8);
  color: white;
  padding: 12px 28px;
  border-radius: 30px;
  font-size: 0.9rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 2px;
  margin-bottom: 25px;
  box-shadow:
    0 8px 25px rgba(59, 130, 246, 0.4),
    0 4px 15px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}

.badge::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.6s;
}

.badge:hover::before {
  left: 100%;
}

.mainTitle {
  font-size: 3.8rem;
  font-weight: 900;
  background: linear-gradient(135deg, #ffffff 0%, #e2e8f0 20%, #3b82f6 40%, #60a5fa 60%, #ffffff 80%, #f1f5f9 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-bottom: 30px;
  line-height: 1.1;
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  animation: titleGlow 4s ease-in-out infinite;
}

.description {
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.8;
  margin-bottom: 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Services Grid */
.servicesGrid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30px;
  margin-top: 60px;
  justify-items: center;
}

.serviceCard {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  padding: 45px 35px;
  border-radius: 25px;
  position: relative;
  overflow: hidden;
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(30px);
  box-shadow:
    0 15px 40px rgba(0, 0, 0, 0.3),
    0 8px 25px rgba(59, 130, 246, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  cursor: pointer;
  -webkit-tap-highlight-color: transparent;
  touch-action: manipulation;
  user-select: none;
}

.serviceCard::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(147, 51, 234, 0.1));
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: 25px;
}

/* Hover effects only for devices that support hover */
@media (hover: hover) {
  .serviceCard:hover {
    transform: translateY(-12px) scale(1.02);
    box-shadow:
      0 30px 60px rgba(0, 0, 0, 0.4),
      0 20px 40px rgba(59, 130, 246, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
    border-color: rgba(59, 130, 246, 0.4);
  }

  .serviceCard:hover::before {
    opacity: 1;
  }
}

/* Touch feedback for touch devices */
.serviceCard:active {
  transform: translateY(-6px) scale(1.01);
  transition: all 0.1s ease;
}

.serviceIcon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #3b82f6, #2563eb, #1d4ed8);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 2rem;
  margin-bottom: 30px;
  box-shadow:
    0 12px 30px rgba(59, 130, 246, 0.4),
    0 6px 15px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.serviceIcon::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transform: rotate(45deg);
  transition: transform 0.6s;
}

.serviceCard:hover .serviceIcon {
  transform: scale(1.15) rotate(5deg);
  box-shadow:
    0 20px 45px rgba(59, 130, 246, 0.5),
    0 10px 25px rgba(0, 0, 0, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.serviceCard:hover .serviceIcon::before {
  transform: rotate(45deg) translate(50%, 50%);
}

.serviceContent {
  position: relative;
  z-index: 2;
}

.serviceTitle {
  font-size: 1.5rem;
  font-weight: 800;
  background: linear-gradient(135deg, #ffffff, #e2e8f0);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-bottom: 18px;
  line-height: 1.3;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.serviceDescription {
  font-size: 1.05rem;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.7;
  margin-bottom: 25px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.featuresList {
  list-style: none;
  padding: 0;
  margin: 0;
}

.featureItem {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 12px;
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.9);
  transition: all 0.3s ease;
}

.featureItem:last-child {
  margin-bottom: 0;
}

.featureItem:hover {
  color: #ffffff;
  transform: translateX(5px);
}

.featureItem i {
  color: #60a5fa;
  font-size: 0.7rem;
  width: 20px;
  height: 20px;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.2), rgba(37, 99, 235, 0.1));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  border: 1px solid rgba(59, 130, 246, 0.3);
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.2);
  transition: all 0.3s ease;
}

.featureItem:hover i {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.4), rgba(37, 99, 235, 0.2));
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
  transform: scale(1.1);
}

.serviceGlow {
  position: absolute;
  top: -50%;
  left: -50%;
  right: -50%;
  bottom: -50%;
  background: radial-gradient(circle, rgba(59, 130, 246, 0.15) 0%, rgba(147, 51, 234, 0.1) 50%, transparent 70%);
  opacity: 0;
  transition: opacity 0.4s ease;
  animation: glowPulse 3s ease-in-out infinite;
}

.serviceCard:hover .serviceGlow {
  opacity: 1;
}

/* Animations */
.fadeInUp {
  animation: fadeInUp 0.8s ease-out;
}

.slideInUp {
  animation: slideInUp 0.6s ease-out var(--delay, 0s) both;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(40px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes backgroundShift {
  0%, 100% {
    transform: translateX(0) translateY(0);
  }
  25% {
    transform: translateX(20px) translateY(-10px);
  }
  50% {
    transform: translateX(-15px) translateY(15px);
  }
  75% {
    transform: translateX(10px) translateY(-20px);
  }
}

@keyframes titleGlow {
  0%, 100% {
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  }
  50% {
    text-shadow:
      0 4px 8px rgba(0, 0, 0, 0.3),
      0 0 20px rgba(59, 130, 246, 0.4),
      0 0 40px rgba(59, 130, 246, 0.2);
  }
}

@keyframes glowPulse {
  0%, 100% {
    transform: scale(1);
    opacity: 0;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.3;
  }
}

/* Responsive Design */

/* Large Desktop (1440px+) */
@media (min-width: 1440px) {
  .container {
    padding: 0 40px;
  }

  .servicesGrid {
    grid-template-columns: repeat(3, 1fr);
    gap: 40px;
    max-width: 1300px;
    margin: 60px auto 0;
  }

  .serviceCard {
    padding: 50px 40px;
  }
}

/* Desktop (1024px - 1439px) */
@media (max-width: 1439px) and (min-width: 1024px) {
  .container {
    padding: 0 30px;
  }

  .servicesGrid {
    grid-template-columns: repeat(3, 1fr);
    gap: 35px;
    max-width: 1200px;
    margin: 60px auto 0;
  }

  .serviceCard {
    padding: 45px 35px;
  }
}

/* Tablet Landscape (768px - 1023px) */
@media (max-width: 1023px) and (min-width: 768px) {
  .ourServicesSection {
    padding: 100px 0;
  }

  .container {
    padding: 0 25px;
  }

  .servicesGrid {
    grid-template-columns: repeat(2, 1fr);
    gap: 30px;
    margin-top: 50px;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
  }

  .serviceCard {
    padding: 40px 30px;
  }

  .serviceIcon {
    width: 75px;
    height: 75px;
    font-size: 1.8rem;
    margin-bottom: 25px;
  }

  .serviceTitle {
    font-size: 1.4rem;
  }

  .serviceDescription {
    font-size: 1.05rem;
  }
}

/* Tablet Portrait (481px - 767px) */
@media (max-width: 767px) and (min-width: 481px) {
  .ourServicesSection {
    padding: 80px 0;
  }

  .container {
    padding: 0 20px;
  }

  .headerSection {
    margin-bottom: 60px;
  }

  .badge {
    padding: 10px 24px;
    font-size: 0.8rem;
    letter-spacing: 1.5px;
  }

  .mainTitle {
    font-size: 3rem;
    line-height: 1.2;
  }

  .description {
    font-size: 1.15rem;
  }

  .servicesGrid {
    grid-template-columns: repeat(2, 1fr);
    gap: 25px;
    margin-top: 50px;
  }

  .serviceCard {
    padding: 35px 25px;
  }

  .serviceIcon {
    width: 70px;
    height: 70px;
    font-size: 1.7rem;
    margin-bottom: 25px;
  }

  .serviceTitle {
    font-size: 1.3rem;
  }

  .serviceDescription {
    font-size: 1rem;
  }

  .featureItem {
    font-size: 0.95rem;
  }

  .featureItem i {
    width: 18px;
    height: 18px;
  }
}

/* Mobile (320px - 480px) */
@media (max-width: 480px) {
  .ourServicesSection {
    padding: 60px 0;
  }

  .container {
    padding: 0 15px;
  }

  .headerSection {
    margin-bottom: 50px;
  }

  .badge {
    padding: 8px 20px;
    font-size: 0.75rem;
    letter-spacing: 1px;
  }

  .mainTitle {
    font-size: 2.5rem;
    line-height: 1.2;
  }

  .description {
    font-size: 1rem;
  }

  .servicesGrid {
    grid-template-columns: 1fr;
    gap: 25px;
    margin-top: 40px;
  }

  .serviceCard {
    padding: 30px 25px;
    max-width: 400px;
    margin: 0 auto;
  }

  .serviceIcon {
    width: 65px;
    height: 65px;
    font-size: 1.6rem;
    margin-bottom: 20px;
  }

  .serviceTitle {
    font-size: 1.25rem;
  }

  .serviceDescription {
    font-size: 0.95rem;
  }

  .featureItem {
    font-size: 0.9rem;
  }

  .featureItem i {
    width: 16px;
    height: 16px;
  }
}
