/* Partner Canada Universities Section */
.partnerSection {
  position: relative;
  padding: 8rem 0;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 25%, #0f172a 50%, #1e293b 75%, #0f172a 100%);
  overflow: hidden;
}

/* Background Elements */
.backgroundElements {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.bgShape1,
.bgShape2,
.bgShape3 {
  position: absolute;
  border-radius: 50%;
  opacity: 0.06;
  animation: float 12s ease-in-out infinite;
}

.bgShape1 {
  width: 600px;
  height: 600px;
  background: linear-gradient(45deg, #dc2626, #ffffff);
  top: -20%;
  right: -15%;
  animation-delay: 0s;
}

.bgShape2 {
  width: 400px;
  height: 400px;
  background: linear-gradient(45deg, #ffffff, #dc2626);
  bottom: -15%;
  left: -10%;
  animation-delay: 6s;
}

.bgShape3 {
  width: 300px;
  height: 300px;
  background: linear-gradient(45deg, #dc2626, #ffffff);
  top: 30%;
  left: 50%;
  animation-delay: 3s;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-30px) rotate(180deg); }
}

/* Container */
.container {
  position: relative;
  z-index: 10;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 1rem;
}

@media (min-width: 1024px) {
  .container {
    padding: 0 2rem;
  }
}

/* Section Header */
.sectionHeader {
  text-align: center;
  margin-bottom: 5rem;
}

.sectionTitle {
  font-size: 3.5rem;
  font-weight: 900;
  background: linear-gradient(90deg, #dc2626 0%, #ffffff 50%, #dc2626 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  color: #dc2626;
  margin-bottom: 1rem;
  line-height: 1.2;
  filter: drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.3));
}

@media (max-width: 768px) {
  .sectionTitle {
    font-size: 2.5rem;
  }
}

.sectionSubtitle {
  font-size: 1.3rem;
  color: #cbd5e1;
  margin-bottom: 2rem;
  font-weight: 600;
}

.sectionDescription {
  max-width: 800px;
  margin: 0 auto;
  color: #94a3b8;
  font-size: 1.1rem;
  line-height: 1.7;
}

.sectionDescription p {
  margin-bottom: 1.5rem;
}

/* Main Content */
.mainContent {
  display: grid;
  grid-template-columns: 1fr;
  gap: 4rem;
  margin-bottom: 6rem;
}

@media (min-width: 1024px) {
  .mainContent {
    grid-template-columns: 2fr 1fr;
    gap: 5rem;
  }
}

/* Left Content - Universities List */
.leftContent {
  order: 2;
}

@media (min-width: 1024px) {
  .leftContent {
    order: 1;
  }
}

.universitiesSection {
  background: rgba(255, 255, 255, 0.03);
  border-radius: 20px;
  padding: 2.5rem;
  border: 1px solid rgba(220, 38, 38, 0.2);
  backdrop-filter: blur(10px);
}

.brandHeader {
  margin-bottom: 2rem;
}

.brandTitle {
  font-size: 1.8rem;
  font-weight: 700;
  color: #dc2626;
  margin-bottom: 1rem;
}

.listTitle {
  font-size: 1.2rem;
  color: #e2e8f0;
  font-weight: 600;
  line-height: 1.5;
}

.universitiesList {
  display: grid;
  grid-template-columns: 1fr;
  gap: 0.75rem;
}

@media (min-width: 640px) {
  .universitiesList {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }
}

.universityItem {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  transition: all 0.3s ease;
  border: 1px solid transparent;
}

.universityItem:hover {
  background: rgba(220, 38, 38, 0.1);
  border-color: rgba(220, 38, 38, 0.3);
  transform: translateX(5px);
}

.universityItem i {
  color: #dc2626;
  font-size: 0.9rem;
  flex-shrink: 0;
}

.universityItem span {
  color: #f1f5f9;
  font-size: 0.95rem;
  font-weight: 500;
}

/* Right Content - Stats and Advantages */
.rightContent {
  order: 1;
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

@media (min-width: 1024px) {
  .rightContent {
    order: 2;
  }
}

/* Stats Card */
.statsCard {
  background: rgba(220, 38, 38, 0.1);
  border-radius: 20px;
  padding: 2rem;
  text-align: center;
  border: 1px solid rgba(220, 38, 38, 0.3);
  backdrop-filter: blur(10px);
}

.statNumber {
  font-size: 3rem;
  font-weight: 900;
  color: #dc2626;
  margin-bottom: 0.5rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.statLabel {
  font-size: 1.1rem;
  color: #f1f5f9;
  font-weight: 600;
  margin-bottom: 1.5rem;
}

.needGuidance h4 {
  font-size: 1.2rem;
  color: #f1f5f9;
  margin-bottom: 0.5rem;
  font-weight: 700;
}

.needGuidance p {
  color: #cbd5e1;
  font-size: 0.9rem;
  margin-bottom: 1.5rem;
  line-height: 1.5;
}

.consultationBtn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: linear-gradient(135deg, #dc2626, #b91c1c);
  color: #ffffff;
  text-decoration: none;
  border-radius: 25px;
  font-weight: 600;
  font-size: 0.9rem;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.consultationBtn:hover {
  background: linear-gradient(135deg, #b91c1c, #991b1b);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(220, 38, 38, 0.4);
}

.consultationBtn i {
  transition: transform 0.3s ease;
}

.consultationBtn:hover i {
  transform: translateX(3px);
}

/* Advantages Grid */
.advantagesGrid {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.advantageCard {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 15px;
  border: 1px solid rgba(220, 38, 38, 0.2);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.advantageCard:hover {
  background: rgba(220, 38, 38, 0.05);
  border-color: rgba(220, 38, 38, 0.3);
  transform: translateY(-2px);
}

.advantageIcon {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #dc2626, #b91c1c);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.advantageIcon i {
  color: #ffffff;
  font-size: 1rem;
}

.advantageContent {
  flex: 1;
}

.advantageTitle {
  font-size: 1rem;
  font-weight: 700;
  color: #f1f5f9;
  margin-bottom: 0.25rem;
}

.advantageDescription {
  font-size: 0.85rem;
  color: #94a3b8;
  line-height: 1.4;
}

/* Bottom CTA Section */
.bottomCTA {
  background: rgba(220, 38, 38, 0.05);
  border-radius: 25px;
  padding: 3rem;
  text-align: center;
  border: 1px solid rgba(220, 38, 38, 0.2);
  backdrop-filter: blur(10px);
}

.ctaContent {
  max-width: 800px;
  margin: 0 auto;
}

.ctaMainTitle {
  font-size: 2.2rem;
  font-weight: 800;
  color: #f1f5f9;
  margin-bottom: 1rem;
  line-height: 1.3;
}

@media (max-width: 768px) {
  .ctaMainTitle {
    font-size: 1.8rem;
  }
}

.ctaMainDescription {
  font-size: 1.1rem;
  color: #cbd5e1;
  margin-bottom: 2rem;
  line-height: 1.6;
}

.featuresRow {
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.featureItem {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #dc2626;
  font-weight: 600;
  font-size: 0.9rem;
}

.featureItem i {
  font-size: 1rem;
}

.ctaStats {
  margin-bottom: 2rem;
}

.ctaStat {
  display: inline-block;
  text-align: center;
}

.ctaStatNumber {
  font-size: 2.5rem;
  font-weight: 900;
  color: #dc2626;
  margin-bottom: 0.25rem;
}

.ctaStatLabel {
  font-size: 1rem;
  color: #f1f5f9;
  font-weight: 600;
  margin-bottom: 0.25rem;
}

.ctaStatDesc {
  font-size: 0.9rem;
  color: #94a3b8;
}

.ctaButtons {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.primaryCtaBtn {
  display: inline-flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem 2rem;
  background: linear-gradient(135deg, #dc2626, #b91c1c);
  color: #ffffff;
  text-decoration: none;
  border-radius: 30px;
  font-weight: 700;
  font-size: 1.1rem;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.primaryCtaBtn:hover {
  background: linear-gradient(135deg, #b91c1c, #991b1b);
  transform: translateY(-3px);
  box-shadow: 0 10px 25px rgba(220, 38, 38, 0.4);
}

.primaryCtaBtn i {
  transition: transform 0.3s ease;
}

.primaryCtaBtn:hover i {
  transform: translateX(3px);
}

.ctaDisclaimer {
  font-size: 0.85rem;
  color: #64748b;
  font-style: italic;
}

/* Animations */
.fadeInUp {
  animation: fadeInUp 0.8s ease forwards;
}

.slideInLeft {
  animation: slideInLeft 0.8s ease forwards;
}

.slideInRight {
  animation: slideInRight 0.8s ease forwards;
}

.fadeInItem {
  animation: fadeInItem 0.6s ease forwards;
  animation-delay: var(--delay, 0s);
  opacity: 0;
  transform: translateY(20px);
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInItem {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .partnerSection {
    padding: 4rem 0;
  }
  
  .sectionHeader {
    margin-bottom: 3rem;
  }
  
  .mainContent {
    gap: 2rem;
    margin-bottom: 3rem;
  }
  
  .universitiesSection {
    padding: 1.5rem;
  }
  
  .bottomCTA {
    padding: 2rem;
  }
  
  .featuresRow {
    gap: 1rem;
  }
}
