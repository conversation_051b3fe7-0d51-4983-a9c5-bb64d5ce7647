/* About Education Component Styles */

.aboutEducationSection {
  padding: 50px 0;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 30%, #334155 70%, #475569 100%);
  position: relative;
  overflow: hidden;
}

/* Enhanced Background Effects */
.aboutEducationSection::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 25% 25%, rgba(59, 130, 246, 0.12) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(147, 51, 234, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 50% 10%, rgba(34, 197, 94, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 10% 80%, rgba(236, 72, 153, 0.09) 0%, transparent 50%);
  background-size: 500px 500px, 400px 400px, 350px 350px, 450px 450px;
  animation: backgroundOrbs 20s ease-in-out infinite;
  z-index: 1;
}



/* Animated Background Elements */
.backgroundElements {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
  pointer-events: none;
}

.floatingShape {
  position: absolute;
  border-radius: 50%;
  animation: floatShape 15s ease-in-out infinite;
}

.floatingShape:nth-child(odd) {
  background: radial-gradient(circle, rgba(59, 130, 246, 0.8) 0%, rgba(147, 197, 253, 0.4) 50%, transparent 100%);
  box-shadow: 0 0 30px rgba(59, 130, 246, 0.6);
}

.floatingShape:nth-child(even) {
  background: radial-gradient(circle, rgba(147, 51, 234, 0.8) 0%, rgba(196, 181, 253, 0.4) 50%, transparent 100%);
  box-shadow: 0 0 25px rgba(147, 51, 234, 0.6);
}

.floatingShape:nth-child(1) { width: 60px; height: 60px; top: 10%; left: 5%; animation-delay: 0s; }
.floatingShape:nth-child(2) { width: 40px; height: 40px; top: 20%; right: 10%; animation-delay: -2s; }
.floatingShape:nth-child(3) { width: 80px; height: 80px; top: 60%; left: 8%; animation-delay: -4s; }
.floatingShape:nth-child(4) { width: 50px; height: 50px; bottom: 20%; right: 15%; animation-delay: -6s; }
.floatingShape:nth-child(5) { width: 35px; height: 35px; top: 40%; left: 15%; animation-delay: -8s; }
.floatingShape:nth-child(6) { width: 70px; height: 70px; bottom: 40%; left: 20%; animation-delay: -10s; }
.floatingShape:nth-child(7) { width: 45px; height: 45px; top: 80%; right: 25%; animation-delay: -12s; }
.floatingShape:nth-child(8) { width: 55px; height: 55px; top: 30%; right: 30%; animation-delay: -14s; }

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 15px;
  position: relative;
  z-index: 2;
}

/* Main Heading - Full Width */
.mainHeading {
  text-align: center;
  max-width: 1400px;
  width: 100%;
  padding: 0 20px 20px 20px;
  animation: fadeInUp 1s ease-out;
  overflow: visible;
  margin: 0 auto;
}

.sectionTitle {
  font-size: 3.8rem;
  font-weight: 800;
  background: linear-gradient(135deg, #ffffff 0%, #e2e8f0 30%, #ffffff 60%, #f1f5f9 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  line-height: 1.2;
  margin: 0;
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.4);
  overflow: visible;
}

.contentWrapper {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 80px;
  align-items: center;
  min-height: 500px;
}

/* Left Content */
.leftContent {
  animation: fadeInLeft 1s ease-out 0.3s both;
}

.titleHighlight {
  background: linear-gradient(135deg, #60a5fa 0%, #3b82f6 50%, #2563eb 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.mainContent {
  display: flex;
  flex-direction: column;
  gap: 30px;
}



.description {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.7;
  margin: 0;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.featuresList {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.featureItem {
  display: flex;
  align-items: center;
  gap: 25px;
  padding: 25px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.08));
  border-radius: 20px;
  box-shadow:
    0 15px 50px rgba(0, 0, 0, 0.4),
    0 5px 20px rgba(96, 165, 250, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  animation: featureSlideIn 0.8s ease-out var(--delay) both;
  border: 2px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(30px);
  position: relative;
  overflow: hidden;
}

.featureItem::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.03), transparent);
  transform: rotate(45deg);
  animation: featureShimmer 5s ease-in-out infinite;
  pointer-events: none;
}

.featureItem:hover {
  transform: translateX(15px) scale(1.02);
  box-shadow:
    0 25px 70px rgba(96, 165, 250, 0.3),
    0 10px 30px rgba(0, 0, 0, 0.3),
    inset 0 2px 0 rgba(255, 255, 255, 0.3);
  border-color: rgba(96, 165, 250, 0.3);
}

.featureIcon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #60a5fa, #3b82f6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.4rem;
  flex-shrink: 0;
  box-shadow:
    0 10px 25px rgba(96, 165, 250, 0.4),
    0 5px 15px rgba(0, 0, 0, 0.3);
  position: relative;
  z-index: 2;
}

.featureIcon::before {
  content: '';
  position: absolute;
  top: -5px;
  left: -5px;
  right: -5px;
  bottom: -5px;
  background: linear-gradient(135deg, #60a5fa, #3b82f6);
  border-radius: 50%;
  z-index: -1;
  opacity: 0.3;
  animation: iconGlow 2s ease-in-out infinite alternate;
}

.featureText {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
  line-height: 1.5;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  position: relative;
  z-index: 2;
}



/* Right Content - Image */
.rightContent {
  animation: fadeInRight 1s ease-out 0.3s both;
}

.imageContainer {
  position: relative;
  height: 600px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.imageWrapper {
  position: relative;
  width: 100%;
  max-width: 500px;
  height: 500px;
  border-radius: 30px;
  overflow: hidden;
  box-shadow: 0 25px 80px rgba(0, 0, 0, 0.15);
  transform: rotate(3deg);
  transition: all 0.4s ease;
}

.imageWrapper:hover {
  transform: rotate(0deg) scale(1.02);
  box-shadow: 0 35px 100px rgba(43, 112, 250, 0.2);
}

.aboutImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all 0.4s ease;
}

.imageOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(43, 112, 250, 0.1), transparent);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.imageWrapper:hover .imageOverlay {
  opacity: 1;
}

/* Floating Elements */
.floatingElement1,
.floatingElement2,
.floatingElement3 {
  position: absolute;
  z-index: 3;
}

.floatingElement1 {
  top: 30%;
  left: -30px;
  animation: floatElement 4s ease-in-out infinite;
}

.floatingElement2 {
  bottom: 20%;
  left: -30px;
  animation: floatElement 4s ease-in-out infinite 1.5s;
}

.floatingElement3 {
  top: 50%;
  right: -40px;
  animation: floatElement 4s ease-in-out infinite 3s;
}

.floatingCard {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1));
  padding: 18px 25px;
  border-radius: 20px;
  box-shadow:
    0 15px 40px rgba(0, 0, 0, 0.3),
    0 5px 15px rgba(96, 165, 250, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  display: flex;
  align-items: center;
  gap: 12px;
  border: 2px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(20px);
  transition: all 0.3s ease;
}

.floatingCard:hover {
  transform: scale(1.05);
  box-shadow:
    0 20px 50px rgba(96, 165, 250, 0.3),
    0 8px 20px rgba(0, 0, 0, 0.2),
    inset 0 2px 0 rgba(255, 255, 255, 0.4);
}

.floatingCard i {
  color: #60a5fa;
  font-size: 1.2rem;
  text-shadow: 0 0 10px rgba(96, 165, 250, 0.5);
}

.floatingCard span {
  font-weight: 600;
  color: white;
  font-size: 1rem;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes floatShape {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  25% {
    transform: translateY(-20px) rotate(90deg);
  }
  50% {
    transform: translateY(-10px) rotate(180deg);
  }
  75% {
    transform: translateY(-30px) rotate(270deg);
  }
}

@keyframes bookFlip {
  0%, 100% {
    transform: rotateY(0deg);
  }
  50% {
    transform: rotateY(180deg);
  }
}

@keyframes featureSlideIn {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}



@keyframes floatElement {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-15px);
  }
}

@keyframes backgroundOrbs {
  0%, 100% {
    transform: scale(1) rotate(0deg);
    opacity: 0.8;
  }
  25% {
    transform: scale(1.1) rotate(90deg);
    opacity: 1;
  }
  50% {
    transform: scale(0.9) rotate(180deg);
    opacity: 0.6;
  }
  75% {
    transform: scale(1.05) rotate(270deg);
    opacity: 0.9;
  }
}





@keyframes featureShimmer {
  0% {
    transform: translateX(-100%) translateY(-100%) rotate(45deg);
  }
  50% {
    transform: translateX(100%) translateY(100%) rotate(45deg);
  }
  100% {
    transform: translateX(200%) translateY(200%) rotate(45deg);
  }
}

@keyframes iconGlow {
  0% {
    opacity: 0.3;
    transform: scale(1);
  }
  100% {
    opacity: 0.6;
    transform: scale(1.1);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .aboutEducationSection {
    padding: 30px 0;
  }

  .mainHeading {
    margin: 0 auto;
  }

  .sectionTitle {
    font-size: 2.5rem;
  }

  .contentWrapper {
    grid-template-columns: 1fr;
    gap: 50px;
  }

  .imageContainer {
    height: 400px;
  }

  .imageWrapper {
    height: 350px;
    transform: rotate(0deg);
  }

  .floatingElement1,
  .floatingElement2,
  .floatingElement3 {
    display: none;
  }

  .featureItem {
    padding: 15px;
  }

  .featureIcon {
    width: 40px;
    height: 40px;
    font-size: 1rem;
  }
}
