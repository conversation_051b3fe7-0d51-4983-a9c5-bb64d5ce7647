/* <PERSON><PERSON>gin Component Styles */

.loginContainer {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 25%, #334155 50%, #1e293b 75%, #0f172a 100%);
  position: relative;
  overflow: hidden;
  padding: 20px;
}

.loginContainer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(147, 51, 234, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 50% 50%, rgba(16, 185, 129, 0.1) 0%, transparent 70%);
  pointer-events: none;
  animation: backgroundPulse 25s ease-in-out infinite;
}

@keyframes backgroundPulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

.loginCard {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  border-radius: 24px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(30px);
  padding: 40px;
  width: 100%;
  max-width: 450px;
  box-shadow: 
    0 20px 50px rgba(0, 0, 0, 0.3),
    0 10px 30px rgba(59, 130, 246, 0.1);
  position: relative;
  z-index: 2;
}

.logoSection {
  text-align: center;
  margin-bottom: 40px;
}

.title {
  font-size: 2.5rem;
  font-weight: 900;
  background: linear-gradient(135deg, #ffffff 0%, #3b82f6 50%, #60a5fa 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-bottom: 10px;
  line-height: 1.1;
}

.subtitle {
  color: #94a3b8;
  font-size: 1.1rem;
  font-weight: 500;
}

.loginForm {
  display: flex;
  flex-direction: column;
  gap: 25px;
}

.inputGroup {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.label {
  font-size: 1rem;
  font-weight: 600;
  color: #e2e8f0;
  margin-bottom: 5px;
}

.input {
  padding: 16px 20px;
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.05);
  color: #ffffff;
  font-size: 1rem;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.input:focus {
  outline: none;
  border-color: #3b82f6;
  background: rgba(255, 255, 255, 0.08);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.input::placeholder {
  color: #64748b;
}

.input:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.loginButton {
  padding: 16px 24px;
  background: linear-gradient(135deg, #3b82f6, #2563eb, #1d4ed8);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 1.1rem;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  margin-top: 10px;
  box-shadow: 
    0 8px 25px rgba(59, 130, 246, 0.4),
    0 4px 15px rgba(0, 0, 0, 0.3);
}

.loginButton:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 
    0 12px 35px rgba(59, 130, 246, 0.5),
    0 6px 20px rgba(0, 0, 0, 0.4);
}

.loginButton:active:not(:disabled) {
  transform: translateY(0);
}

.loginButton:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.loginButton.loading {
  background: linear-gradient(135deg, #64748b, #475569, #334155);
}

.errorMessage {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.2), rgba(220, 38, 38, 0.1));
  border: 1px solid rgba(239, 68, 68, 0.3);
  border-radius: 12px;
  padding: 16px;
  color: #fca5a5;
  font-size: 0.95rem;
  display: flex;
  align-items: center;
  gap: 10px;
  backdrop-filter: blur(10px);
}

.footer {
  margin-top: 30px;
  text-align: center;
}

.footerText {
  color: #64748b;
  font-size: 0.9rem;
  line-height: 1.5;
}

/* Responsive Design */
@media (max-width: 768px) {
  .loginContainer {
    padding: 15px;
  }
  
  .loginCard {
    padding: 30px 25px;
  }
  
  .title {
    font-size: 2rem;
  }
  
  .subtitle {
    font-size: 1rem;
  }
}
