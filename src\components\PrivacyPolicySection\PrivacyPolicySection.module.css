/* Privacy Policy Section Component Styles */

.privacySection {
  padding: 120px 0;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 25%, #334155 50%, #1e293b 75%, #0f172a 100%);
  position: relative;
  overflow: hidden;
  min-height: 100vh;
}

.privacySection::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(147, 51, 234, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 50% 50%, rgba(16, 185, 129, 0.08) 0%, transparent 70%);
  pointer-events: none;
  animation: backgroundPulse 30s ease-in-out infinite;
}

/* Background Elements */
.backgroundElements {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
  pointer-events: none;
}

.floatingShape1,
.floatingShape2,
.floatingShape3 {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.08), rgba(147, 51, 234, 0.08));
  animation: floatShape 35s ease-in-out infinite;
}

.floatingShape1 {
  width: 350px;
  height: 350px;
  top: 15%;
  left: 8%;
  animation-delay: 0s;
}

.floatingShape2 {
  width: 200px;
  height: 200px;
  top: 65%;
  right: 12%;
  animation-delay: -12s;
}

.floatingShape3 {
  width: 150px;
  height: 150px;
  top: 35%;
  right: 30%;
  animation-delay: -24s;
}

.gradientOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(59, 130, 246, 0.02) 50%, transparent 70%);
  animation: gradientShift 40s ease-in-out infinite;
}

.container {
  max-width: 1600px;
  margin: 0 auto;
  padding: 0 20px;
  position: relative;
  z-index: 2;
}

/* Header Section */
.headerSection {
  text-align: center;
  margin-bottom: 80px;
}

.badge {
  display: inline-block;
  background: linear-gradient(135deg, #3b82f6, #2563eb, #1d4ed8);
  color: white;
  padding: 12px 28px;
  border-radius: 30px;
  font-size: 0.9rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 2px;
  margin-bottom: 25px;
  box-shadow: 
    0 8px 25px rgba(59, 130, 246, 0.4),
    0 4px 15px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.mainTitle {
  font-size: 4rem;
  font-weight: 800;
  line-height: 1.15;
  background: linear-gradient(135deg, #ffffff 0%, #e2e8f0 30%, #ffffff 60%, #f1f5f9 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.4), 0 0 30px rgba(43, 112, 250, 0.2);
  margin-bottom: 30px;
  position: relative;
  letter-spacing: -0.025em;
  text-align: center;
  animation: titleGlow 4s ease-in-out infinite;
}

.description {
  font-size: 1.3rem;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.8;
  margin-bottom: 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

/* Statistics Background Section */
.statsBackground {
  text-align: center;
  margin-bottom: 80px;
  padding: 60px 40px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.08), rgba(255, 255, 255, 0.03));
  border-radius: 30px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  box-shadow: 
    0 20px 50px rgba(0, 0, 0, 0.2),
    0 10px 30px rgba(59, 130, 246, 0.1);
  position: relative;
  overflow: hidden;
}

.statsBackground::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(59, 130, 246, 0.05) 50%, transparent 70%);
  pointer-events: none;
}

.statsHeader {
  margin-bottom: 50px;
  position: relative;
  z-index: 2;
}

.statsTitle {
  font-size: 3rem;
  font-weight: 900;
  background: linear-gradient(135deg, #ffffff 0%, #60a5fa 50%, #ffffff 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-bottom: 15px;
  line-height: 1.2;
}

.statsSubtitle {
  font-size: 1.5rem;
  color: #60a5fa;
  font-weight: 600;
  margin-bottom: 15px;
}

.statsDescription {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
  max-width: 600px;
  margin: 0 auto;
}

.statsContainer {
  display: flex;
  justify-content: center;
  gap: 30px;
  margin-bottom: 50px;
  flex-wrap: wrap;
  position: relative;
  z-index: 2;
}

.statItem {
  position: relative;
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  backdrop-filter: blur(20px);
  padding: 30px 25px;
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  text-align: center;
  min-width: 180px;
  max-width: 220px;
  flex: 1;
}

.statItem:hover {
  transform: translateY(-10px);
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.3),
    0 0 40px rgba(59, 130, 246, 0.3);
}

.statIcon {
  font-size: 2.2rem;
  margin-bottom: 15px;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.statNumber {
  font-size: 3rem;
  font-weight: 900;
  background: linear-gradient(135deg, #60a5fa, #a78bfa);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 10px;
  line-height: 1;
}

.statLabel {
  font-size: 1rem;
  color: #cbd5e1;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.statGlow {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80px;
  height: 80px;
  background: radial-gradient(circle, rgba(59, 130, 246, 0.3), transparent);
  border-radius: 50%;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.statItem:hover .statGlow {
  opacity: 1;
}

.statItem1 { animation: slideInLeft 0.8s ease-out 0.1s both; }
.statItem2 { animation: slideInLeft 0.8s ease-out 0.2s both; }
.statItem3 { animation: slideInLeft 0.8s ease-out 0.3s both; }

.studyDestinations {
  position: relative;
  z-index: 2;
}

.destinationsTitle {
  font-size: 1.8rem;
  font-weight: 700;
  color: white;
  margin-bottom: 30px;
  background: linear-gradient(135deg, #ffffff, #e2e8f0);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.destinationsList {
  display: flex;
  justify-content: center;
  gap: 25px;
  flex-wrap: wrap;
}

.destinationItem {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 12px 20px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  border-radius: 15px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.destinationItem:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.08));
}

.flagIcon {
  width: 24px;
  height: 18px;
  border-radius: 3px;
  object-fit: cover;
}

/* Content Wrapper */
.contentWrapper {
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: 60px;
  align-items: flex-start;
}

/* Table of Contents */
.tableOfContents {
  position: sticky;
  top: 120px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  border-radius: 20px;
  padding: 30px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(30px);
  box-shadow: 
    0 15px 40px rgba(0, 0, 0, 0.2),
    0 8px 25px rgba(59, 130, 246, 0.1);
  max-height: calc(100vh - 200px);
  overflow-y: auto;
}

.tocTitle {
  font-size: 1.2rem;
  font-weight: 700;
  color: white;
  margin-bottom: 20px;
  text-align: center;
  background: linear-gradient(135deg, #ffffff, #e2e8f0);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
}

.tocTitle i {
  color: #60a5fa;
  font-size: 1.1rem;
}

.tocNav {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.tocItem {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 15px;
  background: none;
  border: none;
  border-radius: 12px;
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
  font-weight: 500;
  text-align: left;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  animation: slideInUp 0.4s ease-out var(--delay, 0s) both;
}

.tocItem:hover {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.2), rgba(147, 51, 234, 0.1));
  color: white;
  transform: translateX(5px);
}

.tocItem.active {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.3), rgba(147, 51, 234, 0.2));
  color: white;
  border-left: 3px solid #3b82f6;
}

.tocItem i {
  width: 16px;
  text-align: center;
  color: #60a5fa;
  flex-shrink: 0;
}

/* Main Content */
.mainContent {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.08), rgba(255, 255, 255, 0.04));
  border-radius: 25px;
  padding: 50px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(30px);
  box-shadow: 
    0 20px 50px rgba(0, 0, 0, 0.2),
    0 10px 30px rgba(59, 130, 246, 0.1);
}

/* Introduction Section */
.introSection {
  margin-bottom: 50px;
  padding-bottom: 30px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.introText {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.7;
  margin-bottom: 20px;
}

.emailLink {
  color: #60a5fa;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s ease;
}

.emailLink:hover {
  color: #3b82f6;
  text-decoration: underline;
}

.warningBox {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.1), rgba(220, 38, 38, 0.05));
  border: 1px solid rgba(239, 68, 68, 0.3);
  border-radius: 15px;
  padding: 20px;
  margin-top: 30px;
  display: flex;
  align-items: flex-start;
  gap: 15px;
}

.warningBox i {
  color: #f87171;
  font-size: 1.2rem;
  margin-top: 2px;
  flex-shrink: 0;
}

.warningBox p {
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
  font-weight: 500;
}

/* Content Sections */
.contentSection {
  margin-bottom: 50px;
  scroll-margin-top: 120px;
}

.sectionTitle {
  font-size: 2rem;
  font-weight: 800;
  background: linear-gradient(135deg, #ffffff, #e2e8f0);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-bottom: 25px;
  line-height: 1.3;
  display: flex;
  align-items: center;
  gap: 15px;
}

.sectionTitle i {
  color: #60a5fa;
  font-size: 1.5rem;
}

.subSectionTitle {
  font-size: 1.4rem;
  font-weight: 700;
  color: white;
  margin: 30px 0 15px 0;
  line-height: 1.3;
}

.sectionText {
  font-size: 1.05rem;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.7;
  margin-bottom: 20px;
}

.bulletList {
  list-style: none;
  padding: 0;
  margin: 20px 0;
}

.bulletList li {
  position: relative;
  padding-left: 30px;
  margin-bottom: 12px;
  font-size: 1.05rem;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.6;
}

.bulletList li::before {
  content: '';
  position: absolute;
  left: 0;
  top: 12px;
  width: 8px;
  height: 8px;
  background: linear-gradient(135deg, #60a5fa, #3b82f6);
  border-radius: 50%;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

/* Contact Information */
.contactInfo {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-top: 25px;
}

.contactItem {
  display: flex;
  align-items: flex-start;
  gap: 15px;
  padding: 20px;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(147, 51, 234, 0.05));
  border-radius: 15px;
  border: 1px solid rgba(59, 130, 246, 0.2);
  transition: all 0.3s ease;
}

.contactItem:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.2);
}

.contactItem i {
  color: #60a5fa;
  font-size: 1.2rem;
  margin-top: 2px;
  flex-shrink: 0;
}

.contactItem div {
  flex: 1;
}

.contactItem strong {
  color: white;
  display: block;
  margin-bottom: 5px;
  font-weight: 600;
}

.contactItem span {
  color: rgba(255, 255, 255, 0.9);
  display: block;
  line-height: 1.5;
  font-size: 0.95rem;
}

.phoneLink {
  color: #60a5fa;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
}

.phoneLink:hover {
  color: #3b82f6;
  text-decoration: underline;
}

/* Animations */
.fadeInUp {
  animation: fadeInUp 0.8s ease-out;
}

.slideInLeft {
  animation: slideInLeft 0.8s ease-out;
}

.slideInRight {
  animation: slideInRight 0.8s ease-out;
}

.slideInUp {
  animation: slideInUp 0.4s ease-out var(--delay, 0s) both;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes backgroundPulse {
  0%, 100% {
    transform: scale(1) rotate(0deg);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.1) rotate(180deg);
    opacity: 1;
  }
}

@keyframes floatShape {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.3;
  }
  50% {
    transform: translateY(-60px) rotate(180deg);
    opacity: 0.6;
  }
}

@keyframes gradientShift {
  0%, 100% {
    transform: translateX(0) translateY(0) rotate(0deg);
  }
  25% {
    transform: translateX(50px) translateY(-40px) rotate(90deg);
  }
  50% {
    transform: translateX(-40px) translateY(50px) rotate(180deg);
  }
  75% {
    transform: translateX(40px) translateY(-30px) rotate(270deg);
  }
}

@keyframes titleGlow {
  0%, 100% {
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  }
  50% {
    text-shadow:
      0 4px 8px rgba(0, 0, 0, 0.3),
      0 0 30px rgba(59, 130, 246, 0.4),
      0 0 60px rgba(59, 130, 246, 0.2);
  }
}

/* Responsive Design */
@media (max-width: 1200px) {
  .container {
    max-width: 1200px;
    padding: 0 30px;
  }

  .contentWrapper {
    grid-template-columns: 280px 1fr;
    gap: 40px;
  }

  .mainContent {
    padding: 40px;
  }

  .mainTitle {
    font-size: 3.5rem;
  }
}

@media (max-width: 992px) {
  .contentWrapper {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .tableOfContents {
    position: static;
    max-height: none;
    order: 2;
    margin-top: 40px;
  }

  .mainContent {
    order: 1;
    padding: 35px;
  }

  .mainTitle {
    font-size: 3rem;
  }

  .description {
    font-size: 1.2rem;
  }
}

@media (max-width: 768px) {
  .privacySection {
    padding: 80px 0;
  }

  .container {
    padding: 0 20px;
  }

  .headerSection {
    margin-bottom: 60px;
  }

  .badge {
    padding: 10px 24px;
    font-size: 0.8rem;
    letter-spacing: 1.5px;
  }

  .mainTitle {
    font-size: 2.8rem;
    line-height: 1.2;
  }

  .description {
    font-size: 1.1rem;
    padding: 0 10px;
  }

  .mainContent {
    padding: 30px 25px;
  }

  .tableOfContents {
    padding: 25px 20px;
  }

  .tocTitle {
    font-size: 1.1rem;
  }

  .tocItem {
    padding: 10px 12px;
    font-size: 0.85rem;
  }

  .sectionTitle {
    font-size: 1.6rem;
    gap: 12px;
  }

  .sectionTitle i {
    font-size: 1.3rem;
  }

  .subSectionTitle {
    font-size: 1.2rem;
  }

  .sectionText {
    font-size: 1rem;
  }

  .bulletList li {
    font-size: 1rem;
    padding-left: 25px;
  }

  .contactItem {
    padding: 15px;
    gap: 12px;
  }

  .contactItem i {
    font-size: 1.1rem;
  }
}

@media (max-width: 640px) {
  .container {
    padding: 0 15px;
  }

  .mainContent {
    padding: 25px 20px;
  }

  .tableOfContents {
    padding: 20px 15px;
  }

  .tocNav {
    gap: 6px;
  }

  .tocItem {
    padding: 8px 10px;
    font-size: 0.8rem;
    gap: 10px;
  }

  .sectionTitle {
    font-size: 1.4rem;
    gap: 10px;
  }

  .sectionTitle i {
    font-size: 1.2rem;
  }

  .contactItem {
    flex-direction: column;
    text-align: center;
    gap: 10px;
  }

  .contactItem i {
    align-self: center;
  }
}

@media (max-width: 480px) {
  .privacySection {
    padding: 60px 0;
  }

  .container {
    padding: 0 12px;
  }

  .headerSection {
    margin-bottom: 40px;
  }

  .badge {
    padding: 8px 20px;
    font-size: 0.75rem;
    letter-spacing: 1px;
  }

  .mainTitle {
    font-size: 2.2rem;
    line-height: 1.1;
  }

  .description {
    font-size: 1rem;
    padding: 0;
  }

  .mainContent {
    padding: 20px 15px;
  }

  .tableOfContents {
    padding: 15px 12px;
  }

  .tocTitle {
    font-size: 1rem;
  }

  .tocItem {
    padding: 6px 8px;
    font-size: 0.75rem;
    gap: 8px;
  }

  .sectionTitle {
    font-size: 1.2rem;
    gap: 8px;
    flex-direction: column;
    align-items: flex-start;
  }

  .sectionTitle i {
    font-size: 1.1rem;
  }

  .subSectionTitle {
    font-size: 1.1rem;
  }

  .sectionText {
    font-size: 0.95rem;
  }

  .bulletList li {
    font-size: 0.95rem;
    padding-left: 20px;
  }

  .contactItem {
    padding: 12px;
  }

  .warningBox {
    padding: 15px;
    gap: 12px;
  }

  .warningBox i {
    font-size: 1.1rem;
  }
}
