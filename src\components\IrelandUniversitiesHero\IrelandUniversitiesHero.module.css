/* Ireland Universities Hero Section */
.irelandHeroSection {
  position: relative;
  min-height: 80vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #0f1419 0%, #065f46 25%, #059669 50%, #f97316 75%, #0f1419 100%);
  overflow: hidden;
  padding: 3.5rem 0;
}

@media (min-width: 1024px) {
  .irelandHeroSection {
    min-height: 85vh;
    padding: 4.5rem 0;
  }
}

/* Background Elements */
.heroBgElements {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.bgElement1,
.bgElement2,
.bgElement3 {
  position: absolute;
  border-radius: 50%;
  animation: float 6s ease-in-out infinite;
}

.bgElement1 {
  width: 300px;
  height: 300px;
  background: rgba(5, 150, 105, 0.15);
  top: 10%;
  left: -10%;
  animation-delay: 0s;
}

.bgElement2 {
  width: 200px;
  height: 200px;
  background: rgba(249, 115, 22, 0.15);
  top: 60%;
  right: -5%;
  animation-delay: 2s;
}

.bgElement3 {
  width: 150px;
  height: 150px;
  background: rgba(5, 150, 105, 0.1);
  bottom: 20%;
  left: 20%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

/* Gradient Overlay */
.heroGradientOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(15, 20, 25, 0.8) 0%, rgba(5, 150, 105, 0.2) 50%, rgba(249, 115, 22, 0.1) 100%);
  z-index: 2;
}

/* Irish Pattern */
.irishPattern {
  position: absolute;
  top: 0;
  right: 0;
  width: 300px;
  height: 200px;
  background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 60 30'%3E%3Cg fill='%23ffffff' fill-opacity='0.03'%3E%3Cpath d='M0 0h60v30H0z'/%3E%3Cpath d='M0 0h20v30H20zM40 0h20v30H60z'/%3E%3Cg fill='%2305a855' fill-opacity='0.05'%3E%3Cpath d='M20 0h20v30H40z'/%3E%3C/g%3E%3Cg fill='%23ffffff' fill-opacity='0.04'%3E%3Cpath d='M5 5l3 3-3 3-3-3zM15 5l3 3-3 3-3-3zM25 5l3 3-3 3-3-3zM35 5l3 3-3 3-3-3zM45 5l3 3-3 3-3-3zM55 5l3 3-3 3-3-3z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E") repeat;
  opacity: 0.5;
  z-index: 2;
}

/* Container */
.container {
  position: relative;
  z-index: 10;
  max-width: 1000px;
  margin: 0 auto;
  padding: 0 1rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: calc(80vh - 7rem);
  gap: 1rem;
}

@media (min-width: 1024px) {
  .container {
    max-width: 1200px;
    min-height: calc(85vh - 9rem);
    padding: 0 2rem;
    gap: 1.5rem;
  }
}

/* Hero Content */
.heroContent {
  text-align: center;
  width: 100%;
}

/* Trust Badge */
.trustBadge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 50px;
  padding: 0.5rem 1rem;
  animation: fadeInUp 0.8s ease-out;
  order: -1;
  align-self: center;
  margin-bottom: 1rem;
}

@media (min-width: 1024px) {
  .trustBadge {
    margin-bottom: 1.5rem;
  }
}

.trustBadge i {
  font-size: 1.2rem;
  color: #059669;
}

.trustText {
  color: white;
  font-weight: 500;
  font-size: 0.9rem;
}

/* Hero Title */
.heroTitle {
  display: flex;
  flex-direction: column;
  gap: 0.4rem;
  margin-bottom: 1.75rem;
  animation: fadeInUp 0.8s ease-out 0.2s both;
}

.titleMain {
  font-size: 3rem;
  font-weight: 900;
  color: #ffffff;
  text-shadow:
    2px 2px 4px rgba(0, 0, 0, 0.8),
    0 0 30px rgba(5, 150, 105, 0.5),
    0 0 60px rgba(249, 115, 22, 0.3);
  line-height: 1.1;
}

.titleHighlight {
  font-size: 2.5rem;
  font-weight: 700;
  background: linear-gradient(90deg, #059669 0%, #f97316 50%, #059669 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  color: #059669;
  line-height: 1.2;
  filter: drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.3));
}

.titleLocation {
  font-size: 2rem;
  font-weight: 600;
  color: #e2e8f0;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
  line-height: 1.3;
}

.titleEducation {
  font-size: 1.5rem;
  font-weight: 500;
  color: #cbd5e1;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
  line-height: 1.4;
}

/* Responsive Typography */
@media (max-width: 480px) {
  .titleMain { font-size: 2.2rem; }
  .titleHighlight { font-size: 1.8rem; }
  .titleLocation { font-size: 1.4rem; }
  .titleEducation { font-size: 1.1rem; }
}

@media (min-width: 481px) and (max-width: 768px) {
  .titleMain { font-size: 2.8rem; }
  .titleHighlight { font-size: 2.2rem; }
  .titleLocation { font-size: 1.6rem; }
  .titleEducation { font-size: 1.3rem; }
}

@media (min-width: 1024px) {
  .titleMain { font-size: 4.5rem; }
  .titleHighlight { font-size: 4rem; }
  .titleLocation { font-size: 3rem; }
  .titleEducation { font-size: 2.5rem; }
}

/* CTA Container */
.ctaContainer {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  animation: fadeInUp 0.8s ease-out 0.6s both;
}

@media (min-width: 640px) {
  .ctaContainer {
    flex-direction: row;
    justify-content: center;
    gap: 1.5rem;
  }
}

/* Buttons */
.primaryBtn,
.secondaryBtn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 2rem;
  border-radius: 50px;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.primaryBtn {
  background: linear-gradient(135deg, #059669 0%, #f97316 50%, #059669 100%);
  color: white;
  box-shadow: 0 10px 25px rgba(5, 150, 105, 0.3);
}

.primaryBtn:hover {
  transform: translateY(-2px);
  box-shadow: 0 15px 35px rgba(5, 150, 105, 0.4);
}

.secondaryBtn {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 2px solid rgba(5, 150, 105, 0.4);
  backdrop-filter: blur(10px);
}

.secondaryBtn:hover {
  background: rgba(5, 150, 105, 0.2);
  border-color: rgba(5, 150, 105, 0.6);
  transform: translateY(-2px);
}

/* Button Icons */
.primaryBtn i,
.secondaryBtn i {
  font-size: 1rem;
  transition: transform 0.3s ease;
}

.primaryBtn:hover i,
.secondaryBtn:hover i {
  transform: translateX(3px);
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}
