/* Blog Section Styles */
.blogSection {
  padding: 120px 0;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 25%, #334155 50%, #1e293b 75%, #0f172a 100%);
  position: relative;
  overflow: hidden;
  min-height: 100vh;
}

/* Loading States */
.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  gap: 1.5rem;
  color: #e2e8f0;
}

.loadingSpinner {
  color: #3b82f6;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.loadingContainer p {
  font-size: 1.125rem;
  font-weight: 500;
  margin: 0;
}

/* Error States */
.errorContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  gap: 1.5rem;
  color: #e2e8f0;
  text-align: center;
  max-width: 500px;
  margin: 0 auto;
}

.errorIcon {
  color: #ef4444;
  opacity: 0.8;
}

.errorContainer h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
  color: #f1f5f9;
}

.errorContainer p {
  font-size: 1rem;
  margin: 0;
  color: #94a3b8;
  line-height: 1.6;
}

.retryButton {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 6px -1px rgba(59, 130, 246, 0.3);
}

.retryButton:hover {
  background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
  transform: translateY(-1px);
  box-shadow: 0 6px 12px -1px rgba(59, 130, 246, 0.4);
}

/* Background Effects */
.backgroundEffects {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
  pointer-events: none;
}

.gradientOrb1,
.gradientOrb2,
.gradientOrb3 {
  position: absolute;
  border-radius: 50%;
  filter: blur(100px);
  opacity: 0.3;
  animation: orbFloat 25s ease-in-out infinite;
}

.gradientOrb1 {
  width: 500px;
  height: 500px;
  background: radial-gradient(circle, #3b82f6 0%, transparent 70%);
  top: 10%;
  right: 10%;
  animation-delay: 0s;
}

.gradientOrb2 {
  width: 400px;
  height: 400px;
  background: radial-gradient(circle, #8b5cf6 0%, transparent 70%);
  bottom: 20%;
  left: 15%;
  animation-delay: -10s;
}

.gradientOrb3 {
  width: 350px;
  height: 350px;
  background: radial-gradient(circle, #06b6d4 0%, transparent 70%);
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  animation-delay: -15s;
}

.floatingParticles {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: linear-gradient(45deg, #3b82f6, #8b5cf6);
  border-radius: 50%;
  opacity: 0.6;
  animation: particleFloat var(--duration, 8s) ease-in-out infinite;
  animation-delay: var(--delay, 0s);
}

.particle:nth-child(odd) {
  background: linear-gradient(45deg, #06b6d4, #3b82f6);
}

.particle:nth-child(1) { top: 10%; left: 10%; }
.particle:nth-child(2) { top: 20%; left: 80%; }
.particle:nth-child(3) { top: 30%; left: 30%; }
.particle:nth-child(4) { top: 40%; left: 70%; }
.particle:nth-child(5) { top: 50%; left: 20%; }
.particle:nth-child(6) { top: 60%; left: 90%; }
.particle:nth-child(7) { top: 70%; left: 40%; }
.particle:nth-child(8) { top: 80%; left: 60%; }
.particle:nth-child(9) { top: 15%; left: 50%; }
.particle:nth-child(10) { top: 25%; left: 15%; }
.particle:nth-child(11) { top: 35%; left: 85%; }
.particle:nth-child(12) { top: 45%; left: 25%; }
.particle:nth-child(13) { top: 55%; left: 75%; }
.particle:nth-child(14) { top: 65%; left: 35%; }
.particle:nth-child(15) { top: 75%; left: 85%; }
.particle:nth-child(16) { top: 85%; left: 45%; }
.particle:nth-child(17) { top: 5%; left: 65%; }
.particle:nth-child(18) { top: 95%; left: 25%; }
.particle:nth-child(19) { top: 12%; left: 35%; }
.particle:nth-child(20) { top: 88%; left: 75%; }

/* Container */
.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 20px;
  position: relative;
  z-index: 2;
}

/* Section Header */
.sectionHeader {
  text-align: center;
  margin-bottom: 80px;
  animation: fadeInUp 1s ease-out;
}

.headerBadge {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(139, 92, 246, 0.1));
  border: 1px solid rgba(59, 130, 246, 0.2);
  padding: 12px 24px;
  border-radius: 50px;
  color: #3b82f6;
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 24px;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.headerBadge:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(59, 130, 246, 0.2);
}

.sectionTitle {
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: 800;
  line-height: 1.1;
  margin-bottom: 24px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.gradientText {
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.titleHighlight {
  color: #ffffff;
  position: relative;
}

.titleHighlight::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 100px;
  height: 4px;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6);
  border-radius: 2px;
}

.sectionDescription {
  font-size: 1.2rem;
  color: #94a3b8;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

/* Filter Section */
.filterSection {
  margin-bottom: 60px;
  animation: fadeInUp 1s ease-out 0.2s both;
}

.searchContainer {
  display: flex;
  justify-content: center;
  margin-bottom: 40px;
}

.searchBox {
  position: relative;
  max-width: 500px;
  width: 100%;
}

.searchBox i {
  position: absolute;
  left: 20px;
  top: 50%;
  transform: translateY(-50%);
  color: #64748b;
  font-size: 18px;
  z-index: 3;
}

.searchInput {
  width: 100%;
  padding: 18px 20px 18px 55px;
  background: rgba(30, 41, 59, 0.8);
  border: 2px solid rgba(59, 130, 246, 0.2);
  border-radius: 25px;
  color: #ffffff;
  font-size: 16px;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.searchInput:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
  transform: translateY(-2px);
}

.searchInput::placeholder {
  color: #64748b;
}

.categoryFilter {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 12px;
}

.categoryButton {
  padding: 12px 24px;
  background: rgba(30, 41, 59, 0.6);
  border: 2px solid rgba(59, 130, 246, 0.2);
  border-radius: 25px;
  color: #94a3b8;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.categoryButton:hover {
  background: rgba(59, 130, 246, 0.1);
  border-color: #3b82f6;
  color: #3b82f6;
  transform: translateY(-2px);
}

.categoryButton.active {
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  border-color: transparent;
  color: #ffffff;
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
}

/* Blog Grid */
.blogGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 40px;
  margin-bottom: 80px;
}

/* Blog Card */
.blogCard {
  background: rgba(30, 41, 59, 0.8);
  border-radius: 24px;
  overflow: hidden;
  border: 1px solid rgba(59, 130, 246, 0.1);
  transition: all 0.4s ease;
  backdrop-filter: blur(10px);
  animation: fadeInUp 0.6s ease-out;
  animation-delay: var(--delay, 0s);
  animation-fill-mode: both;
  position: relative;
}

.blogCard::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.05), rgba(139, 92, 246, 0.05));
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: 24px;
}

.blogCard:hover {
  transform: translateY(-12px);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(59, 130, 246, 0.2);
}

.blogCard:hover::before {
  opacity: 1;
}

.blogCard.featured {
  border: 2px solid rgba(59, 130, 246, 0.3);
  box-shadow: 0 15px 35px rgba(59, 130, 246, 0.2);
}

.blogCard.featured::before {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(139, 92, 246, 0.1));
  opacity: 1;
}

/* Card Image */
.cardImageContainer {
  position: relative;
  height: 250px;
  overflow: hidden;
}

/* Card Header for posts without images */
.cardHeaderNoImage {
  position: relative;
  padding: 20px;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(147, 51, 234, 0.1));
  min-height: 80px;
  display: flex;
  align-items: center;
}

.headerContent {
  display: flex;
  align-items: center;
  gap: 12px;
  width: 100%;
}

.cardImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.4s ease;
}

.blogCard:hover .cardImage {
  transform: scale(1.1);
}

.imageOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.3), rgba(59, 130, 246, 0.2));
  opacity: 0;
  transition: opacity 0.3s ease;
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  padding: 20px;
}

.blogCard:hover .imageOverlay {
  opacity: 1;
}

.overlayContent {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  width: 100%;
}

.categoryBadge {
  background: rgba(59, 130, 246, 0.9);
  color: #ffffff;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  backdrop-filter: blur(10px);
}

.featuredBadge {
  display: flex;
  align-items: center;
  gap: 6px;
  background: linear-gradient(135deg, #f59e0b, #f97316);
  color: #ffffff;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  backdrop-filter: blur(10px);
}

.readTimeIndicator {
  position: absolute;
  bottom: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.8);
  color: #ffffff;
  padding: 8px 12px;
  border-radius: 15px;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 6px;
  backdrop-filter: blur(10px);
}

/* Card Content */
.cardContent {
  padding: 30px;
  position: relative;
  z-index: 2;
}

.cardHeader {
  margin-bottom: 16px;
}

.cardTitle {
  font-size: 1.4rem;
  font-weight: 700;
  color: #ffffff;
  line-height: 1.3;
  margin-bottom: 0;
  transition: color 0.3s ease;
}

.blogCard:hover .cardTitle {
  color: #3b82f6;
}

.cardExcerpt {
  color: #94a3b8;
  line-height: 1.6;
  margin-bottom: 20px;
  font-size: 0.95rem;
}

.cardTags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 24px;
}

.tag {
  background: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
  padding: 6px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  border: 1px solid rgba(59, 130, 246, 0.2);
}

/* Card Footer */
.cardFooter {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.authorSection {
  display: flex;
  align-items: center;
  gap: 12px;
}

.authorAvatar {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  font-size: 16px;
}

.authorInfo {
  display: flex;
  flex-direction: column;
}

.authorName {
  color: #ffffff;
  font-weight: 600;
  font-size: 14px;
}

.postDate {
  color: #64748b;
  font-size: 12px;
}

.viewFullButton {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 50%, #1e40af 100%);
  color: #ffffff;
  border: 2px solid transparent;
  padding: 10px 18px;
  border-radius: 10px;
  font-weight: 600;
  font-size: 13px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  text-transform: uppercase;
  letter-spacing: 0.3px;
  box-shadow: 0 3px 12px rgba(59, 130, 246, 0.25);
  backdrop-filter: blur(10px);
}

.viewFullButton::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s;
}

.viewFullButton:hover {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.9), rgba(147, 51, 234, 0.9));
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
  transition: all 0.2s ease;
}

.viewFullButton:hover::before {
  left: 100%;
}

.viewFullButton:active {
  transform: translateY(0);
  transition: transform 0.1s ease;
}

.viewFullButton svg {
  transform: translateX(0);
  transition: transform 0.3s ease;
  width: 14px;
  height: 14px;
}

.viewFullButton:hover svg {
  transform: translateX(3px);
}

.viewFullButton span {
  position: relative;
  z-index: 2;
}

/* No Results */
.noResults {
  text-align: center;
  padding: 80px 20px;
  animation: fadeInUp 0.6s ease-out;
}

.noResultsIcon {
  width: 80px;
  height: 80px;
  background: rgba(59, 130, 246, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 24px;
  color: #3b82f6;
  font-size: 32px;
}

.noResults h3 {
  color: #ffffff;
  font-size: 1.5rem;
  margin-bottom: 12px;
}

.noResults p {
  color: #94a3b8;
  margin-bottom: 24px;
}

.clearFiltersButton {
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  color: #ffffff;
  border: none;
  padding: 12px 24px;
  border-radius: 20px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.clearFiltersButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(59, 130, 246, 0.4);
}



/* Animations */
@keyframes orbFloat {
  0%, 100% {
    transform: translate(0, 0) rotate(0deg);
  }
  33% {
    transform: translate(30px, -30px) rotate(120deg);
  }
  66% {
    transform: translate(-20px, 20px) rotate(240deg);
  }
}

@keyframes particleFloat {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.6;
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
    opacity: 1;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Design */
/* Tablet Styles */
@media (max-width: 1024px) and (min-width: 769px) {
  .blogGrid {
    grid-template-columns: repeat(2, 1fr);
    gap: 28px;
  }

  .viewFullButton {
    padding: 11px 20px;
    font-size: 13px;
  }

  .viewFullButton svg {
    width: 13px;
    height: 13px;
  }
}

@media (max-width: 768px) {
  .blogSection {
    padding: 80px 0;
  }

  .container {
    padding: 0 16px;
  }

  .sectionHeader {
    margin-bottom: 60px;
  }

  .sectionTitle {
    font-size: 2.5rem;
  }

  .sectionDescription {
    font-size: 1rem;
  }

  .filterSection {
    margin-bottom: 40px;
  }

  .categoryFilter {
    gap: 8px;
    flex-wrap: wrap;
  }

  .categoryButton {
    padding: 10px 16px;
    font-size: 12px;
  }

  .blogGrid {
    grid-template-columns: 1fr;
    gap: 24px;
    margin-bottom: 60px;
  }

  .cardContent {
    padding: 24px;
  }

  .cardTitle {
    font-size: 1.2rem;
    line-height: 1.3;
  }

  .cardExcerpt {
    font-size: 15px;
    line-height: 1.6;
  }

  .cardFooter {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }

  .viewFullButton {
    align-self: stretch;
    justify-content: center;
    padding: 12px 20px;
    font-size: 14px;
    font-weight: 600;
    min-height: 44px;
    border-radius: 12px;
    box-shadow: 0 4px 16px rgba(59, 130, 246, 0.35);
  }

  .viewFullButton:hover {
    transform: translateY(-1px) scale(1.005);
  }

  .viewFullButton svg {
    width: 12px;
    height: 12px;
  }

  .authorSection {
    gap: 12px;
  }

  .authorAvatar {
    width: 36px;
    height: 36px;
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 16px;
  }

  .blogGrid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .searchInput {
    font-size: 14px;
    padding: 12px 16px;
  }

  .categoryFilter {
    justify-content: flex-start;
    overflow-x: auto;
    padding-bottom: 8px;
    gap: 6px;
  }

  .categoryButton {
    white-space: nowrap;
    padding: 8px 14px;
    font-size: 11px;
  }

  .cardContent {
    padding: 20px;
  }

  .cardTitle {
    font-size: 1.1rem;
    line-height: 1.4;
  }

  .cardExcerpt {
    font-size: 14px;
    line-height: 1.5;
  }

  .viewFullButton {
    padding: 10px 16px;
    font-size: 12px;
    min-height: 40px;
    border-radius: 10px;
    gap: 6px;
  }

  .viewFullButton svg {
    width: 10px;
    height: 10px;
  }

  .authorSection {
    gap: 10px;
  }

  .authorName {
    font-size: 13px;
  }

  .postDate {
    font-size: 11px;
  }
}