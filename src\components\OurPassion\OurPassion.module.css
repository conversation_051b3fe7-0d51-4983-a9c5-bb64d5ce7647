/* Our Passion Component Styles */

.passionSection {
  padding: 100px 0;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 30%, #334155 70%, #475569 100%);
  position: relative;
  overflow: hidden;
}

/* Animated Background Effects */
.passionSection::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 15% 25%, rgba(59, 130, 246, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 85% 75%, rgba(147, 51, 234, 0.12) 0%, transparent 50%),
    radial-gradient(circle at 45% 10%, rgba(236, 72, 153, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 40%, rgba(34, 197, 94, 0.08) 0%, transparent 50%);
  background-size: 400px 400px, 350px 350px, 300px 300px, 450px 450px;
  animation: backgroundPulse 15s ease-in-out infinite;
  z-index: 1;
}



/* Floating Particles Background */
.floatingParticles {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
  pointer-events: none;
}

.particle {
  position: absolute;
  border-radius: 50%;
  animation: floatParticle 20s linear infinite;
}

.particle:nth-child(odd) {
  width: 8px;
  height: 8px;
  background: radial-gradient(circle, rgba(59, 130, 246, 1) 0%, rgba(147, 197, 253, 0.6) 50%, transparent 100%);
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.8);
}

.particle:nth-child(even) {
  width: 6px;
  height: 6px;
  background: radial-gradient(circle, rgba(147, 51, 234, 1) 0%, rgba(196, 181, 253, 0.6) 50%, transparent 100%);
  box-shadow: 0 0 15px rgba(147, 51, 234, 0.8);
}

.particle:nth-child(1) { top: 10%; left: 5%; animation-delay: 0s; animation-duration: 25s; }
.particle:nth-child(2) { top: 20%; left: 15%; animation-delay: -3s; animation-duration: 22s; }
.particle:nth-child(3) { top: 30%; left: 85%; animation-delay: -6s; animation-duration: 28s; }
.particle:nth-child(4) { top: 40%; left: 25%; animation-delay: -9s; animation-duration: 24s; }
.particle:nth-child(5) { top: 50%; left: 75%; animation-delay: -12s; animation-duration: 26s; }
.particle:nth-child(6) { top: 60%; left: 35%; animation-delay: -15s; animation-duration: 23s; }
.particle:nth-child(7) { top: 70%; left: 65%; animation-delay: -18s; animation-duration: 27s; }
.particle:nth-child(8) { top: 80%; left: 45%; animation-delay: -21s; animation-duration: 21s; }
.particle:nth-child(9) { top: 15%; left: 55%; animation-delay: -4s; animation-duration: 29s; }
.particle:nth-child(10) { top: 35%; left: 95%; animation-delay: -7s; animation-duration: 20s; }
.particle:nth-child(11) { top: 55%; left: 5%; animation-delay: -10s; animation-duration: 24s; }
.particle:nth-child(12) { top: 75%; left: 85%; animation-delay: -13s; animation-duration: 26s; }

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 15px;
  position: relative;
  z-index: 2;
}

/* Section Header */
.sectionHeader {
  text-align: center;
  margin-bottom: 80px;
  animation: fadeInUp 1s ease-out;
}

.headerBadge {
  display: inline-flex;
  align-items: center;
  gap: 12px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  color: #60a5fa;
  padding: 12px 30px;
  border-radius: 30px;
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 25px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
  text-transform: uppercase;
  letter-spacing: 1px;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.headerBadge i {
  font-size: 1.2rem;
  animation: heartBeat 2s ease-in-out infinite;
  color: #f472b6;
}

.sectionTitle {
  font-size: 3.5rem;
  font-weight: 800;
  background: linear-gradient(135deg, #ffffff 0%, #e2e8f0 30%, #ffffff 60%, #f1f5f9 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-bottom: 30px;
  line-height: 1.2;
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.4);
}

.titleHighlight {
  background: linear-gradient(135deg, #60a5fa 0%, #3b82f6 50%, #2563eb 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.descriptionContainer {
  max-width: 900px;
  margin: 0 auto;
}

.sectionDescription {
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.8;
  margin-bottom: 20px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Services Grid */
.servicesGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 30px;
  margin-bottom: 80px;
}

.serviceCard {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.08));
  padding: 45px;
  border-radius: 25px;
  box-shadow:
    0 25px 80px rgba(0, 0, 0, 0.5),
    0 10px 40px rgba(59, 130, 246, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  border: 2px solid rgba(255, 255, 255, 0.1);
  animation: serviceCardEntrance 0.8s ease-out var(--delay) both;
  backdrop-filter: blur(30px);
}

.serviceCard::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #60a5fa, #3b82f6, #2563eb);
  box-shadow: 0 0 20px rgba(96, 165, 250, 0.5);
}

.serviceCard::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.05), transparent);
  transform: rotate(45deg);
  animation: serviceCardShimmer 5s ease-in-out infinite;
  pointer-events: none;
}

.serviceCard:hover {
  transform: translateY(-15px) scale(1.02);
  box-shadow:
    0 35px 100px rgba(59, 130, 246, 0.4),
    0 15px 50px rgba(0, 0, 0, 0.4),
    inset 0 2px 0 rgba(255, 255, 255, 0.3);
  border-color: rgba(96, 165, 250, 0.3);
}

.serviceCard:hover .serviceGlow {
  opacity: 1;
}

.serviceIcon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #60a5fa, #3b82f6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 2rem;
  margin-bottom: 30px;
  box-shadow:
    0 15px 40px rgba(96, 165, 250, 0.4),
    0 5px 15px rgba(0, 0, 0, 0.3);
  animation: iconFloat 3s ease-in-out infinite;
  position: relative;
}

.serviceIcon::before {
  content: '';
  position: absolute;
  top: -5px;
  left: -5px;
  right: -5px;
  bottom: -5px;
  background: linear-gradient(135deg, #60a5fa, #3b82f6);
  border-radius: 50%;
  z-index: -1;
  opacity: 0.3;
  animation: iconGlow 2s ease-in-out infinite alternate;
}

.serviceContent {
  position: relative;
  z-index: 2;
}

.serviceTitle {
  font-size: 1.6rem;
  font-weight: 700;
  color: white;
  margin-bottom: 15px;
  line-height: 1.3;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.serviceDescription {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.85);
  line-height: 1.6;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.serviceGlow {
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(43, 112, 250, 0.1) 0%, transparent 70%);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

/* Experience Numbers Section */
.experienceSection {
  margin-top: 60px;
}

.experienceHeader {
  text-align: center;
  margin-bottom: 50px;
}

.experienceTitle {
  font-size: 2.5rem;
  font-weight: 700;
  background: linear-gradient(135deg, #ffffff 0%, #e2e8f0 30%, #ffffff 60%, #f1f5f9 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.4);
  margin: 0;
}

.statsGrid {
  display: flex;
  justify-content: space-between;
  gap: 25px;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

.statCard {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.08));
  padding: 40px 30px;
  border-radius: 25px;
  text-align: center;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(30px);
  border: 2px solid rgba(255, 255, 255, 0.1);
  box-shadow:
    0 20px 60px rgba(0, 0, 0, 0.4),
    0 8px 30px rgba(59, 130, 246, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  animation: statCardEntrance 0.8s ease-out both;
  flex: 1;
  min-width: 220px;
}

.statCard:nth-child(1) { animation-delay: 0.1s; }
.statCard:nth-child(2) { animation-delay: 0.2s; }
.statCard:nth-child(3) { animation-delay: 0.3s; }
.statCard:nth-child(4) { animation-delay: 0.4s; }

.statCard::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #60a5fa, #3b82f6, #2563eb);
  box-shadow: 0 0 15px rgba(96, 165, 250, 0.5);
}

.statCard:hover {
  transform: translateY(-10px) scale(1.02);
  box-shadow:
    0 30px 80px rgba(59, 130, 246, 0.3),
    0 12px 40px rgba(0, 0, 0, 0.3),
    inset 0 2px 0 rgba(255, 255, 255, 0.3);
  border-color: rgba(96, 165, 250, 0.3);
}

.statCard:hover .statGlow {
  opacity: 1;
}

.statNumber {
  font-size: 4rem;
  font-weight: 800;
  background: linear-gradient(135deg, #60a5fa 0%, #3b82f6 50%, #2563eb 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  line-height: 1;
  margin-bottom: 15px;
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  animation: numberPulse 3s ease-in-out infinite;
}

.statLabel {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.statGlow {
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(96, 165, 250, 0.1) 0%, transparent 70%);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes floatParticle {
  0% {
    transform: translateY(0px) translateX(0px) scale(0);
    opacity: 0;
  }
  10% {
    opacity: 1;
    transform: translateY(-20px) translateX(10px) scale(1);
  }
  90% {
    opacity: 1;
    transform: translateY(-100vh) translateX(50px) scale(1);
  }
  100% {
    transform: translateY(-120vh) translateX(60px) scale(0);
    opacity: 0;
  }
}

@keyframes heartBeat {
  0%, 100% {
    transform: scale(1);
  }
  25% {
    transform: scale(1.1);
  }
  50% {
    transform: scale(1);
  }
  75% {
    transform: scale(1.05);
  }
}

@keyframes serviceCardEntrance {
  from {
    opacity: 0;
    transform: translateY(50px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes iconFloat {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-8px);
  }
}

@keyframes statCardEntrance {
  from {
    opacity: 0;
    transform: translateY(50px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes numberPulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes backgroundPulse {
  0%, 100% {
    transform: scale(1) rotate(0deg);
    opacity: 0.8;
  }
  25% {
    transform: scale(1.1) rotate(1deg);
    opacity: 1;
  }
  50% {
    transform: scale(0.9) rotate(-1deg);
    opacity: 0.6;
  }
  75% {
    transform: scale(1.05) rotate(0.5deg);
    opacity: 0.9;
  }
}



@keyframes serviceCardShimmer {
  0% {
    transform: translateX(-100%) translateY(-100%) rotate(45deg);
  }
  50% {
    transform: translateX(100%) translateY(100%) rotate(45deg);
  }
  100% {
    transform: translateX(200%) translateY(200%) rotate(45deg);
  }
}

@keyframes iconGlow {
  0% {
    opacity: 0.3;
    transform: scale(1);
  }
  100% {
    opacity: 0.6;
    transform: scale(1.1);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .passionSection {
    padding: 60px 0;
  }

  .sectionTitle {
    font-size: 2.5rem;
  }

  .servicesGrid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .serviceCard {
    padding: 30px;
  }

  .experienceTitle {
    font-size: 2rem;
  }

  .statsGrid {
    flex-wrap: wrap;
    gap: 20px;
    max-width: 100%;
  }

  .statCard {
    padding: 30px 20px;
    flex: 0 0 calc(50% - 10px);
    min-width: 180px;
  }

  .statNumber {
    font-size: 3rem;
  }

  .statLabel {
    font-size: 1rem;
  }
}
