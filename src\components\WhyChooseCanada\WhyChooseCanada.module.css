/* Why Choose Canada Section */
.whyChooseSection {
  padding: 100px 0;
  background: linear-gradient(135deg, 
    #0f172a 0%, 
    #1e293b 25%, 
    #0f172a 50%, 
    #1e293b 75%, 
    #0f172a 100%
  );
  position: relative;
  overflow: hidden;
}

.whyChooseSection::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 20% 20%, rgba(220, 38, 38, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 60%, rgba(220, 38, 38, 0.05) 0%, transparent 50%);
  pointer-events: none;
}

.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
  position: relative;
  z-index: 1;
}

/* Section Header */
.sectionHeader {
  margin-bottom: 80px;
  opacity: 1;
  transform: translateY(0);
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.sectionHeader.fadeInUp {
  opacity: 1;
  transform: translateY(0);
}

.headerContent {
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: 60px;
  align-items: center;
}

.textContent {
  max-width: 600px;
}

.sectionTitle {
  font-size: 3.5rem;
  font-weight: 800;
  color: #f1f5f9;
  margin-bottom: 30px;
  line-height: 1.2;
}

.highlight {
  background: linear-gradient(135deg, #dc2626, #ffffff);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.sectionDescription {
  font-size: 1.2rem;
  color: #cbd5e1;
  line-height: 1.7;
  margin: 0;
}

/* Image Container */
.imageContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

.canadaImage {
  width: 100%;
  height: auto;
  max-width: 280px;
  filter: drop-shadow(0 10px 30px rgba(220, 38, 38, 0.3));
  transition: transform 0.3s ease;
}

.canadaImage:hover {
  transform: scale(1.05);
}

/* Reasons Grid */
.reasonsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
  gap: 30px;
  margin-top: 60px;
}

/* Reason Card */
.reasonCard {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 20px;
  padding: 40px 30px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  opacity: 1;
  transform: translateY(0);
  display: flex;
  align-items: flex-start;
  gap: 20px;
}

.reasonCard.slideInUp {
  opacity: 1;
  transform: translateY(0);
  animation-delay: var(--delay);
  animation-duration: 0.8s;
  animation-fill-mode: both;
}

.reasonCard:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 50px rgba(220, 38, 38, 0.2);
  border-color: rgba(220, 38, 38, 0.3);
  background: rgba(255, 255, 255, 0.08);
}

/* Card Icon */
.cardIcon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #dc2626, #b91c1c);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  box-shadow: 0 8px 25px rgba(220, 38, 38, 0.3);
  transition: all 0.3s ease;
}

.reasonCard:hover .cardIcon {
  transform: scale(1.1);
  box-shadow: 0 12px 35px rgba(220, 38, 38, 0.4);
}

.cardIcon i {
  color: white;
  font-size: 1.5rem;
}

/* Card Content */
.cardContent {
  flex: 1;
}

.cardTitle {
  font-size: 1.4rem;
  font-weight: 700;
  color: #f1f5f9;
  margin-bottom: 12px;
  line-height: 1.3;
}

.cardDescription {
  font-size: 1rem;
  color: #cbd5e1;
  line-height: 1.6;
  margin: 0;
}

/* Animation Classes */
.fadeInUp {
  opacity: 1;
  transform: translateY(0);
  animation: fadeInUp 0.8s ease-out both;
}

.slideInUp {
  opacity: 1;
  transform: translateY(0);
  animation: slideInUp 0.8s ease-out both;
  animation-delay: var(--delay, 0s);
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Design */
@media (max-width: 1024px) {
  .headerContent {
    grid-template-columns: 1fr;
    gap: 40px;
    text-align: center;
  }
  
  .reasonsGrid {
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 25px;
  }
}

@media (max-width: 768px) {
  .whyChooseSection {
    padding: 60px 0;
  }
  
  .container {
    padding: 0 1rem;
  }
  
  .sectionTitle {
    font-size: 2.5rem;
  }
  
  .sectionDescription {
    font-size: 1.1rem;
  }
  
  .reasonsGrid {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .reasonCard {
    padding: 30px 25px;
  }
  
  .canadaImage {
    max-width: 220px;
  }
}

@media (max-width: 480px) {
  .sectionTitle {
    font-size: 2rem;
  }
  
  .reasonCard {
    padding: 25px 20px;
    flex-direction: column;
    text-align: center;
    gap: 15px;
  }
  
  .cardIcon {
    align-self: center;
  }
  
  .canadaImage {
    max-width: 180px;
  }
}
