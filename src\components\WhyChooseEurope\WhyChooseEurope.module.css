/* Why Choose Europe Section */
.whyChooseEurope {
  padding: 5rem 0;
  background: linear-gradient(135deg, #1e293b 0%, #334155 50%, #475569 100%);
  position: relative;
  overflow: hidden;
}

.whyChooseEurope::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="%23475569" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.2;
  z-index: 1;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  position: relative;
  z-index: 2;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
}

@media (max-width: 968px) {
  .container {
    grid-template-columns: 1fr;
    gap: 3rem;
  }
}

/* Images Section */
.imagesSection {
  position: relative;
}

.imageGrid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
  position: relative;
}

.campusImageWrapper,
.libraryImageWrapper {
  position: relative;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.campusImageWrapper:hover,
.libraryImageWrapper:hover {
  transform: translateY(-5px);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
}

.campusImage,
.libraryImage {
  width: 100%;
  height: 300px;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.campusImageWrapper:hover .campusImage,
.libraryImageWrapper:hover .libraryImage {
  transform: scale(1.05);
}

.imageLabel {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  color: white;
  padding: 2rem 1.5rem 1.5rem;
  font-weight: 600;
  font-size: 1.1rem;
}

/* Content Section */
.contentSection {
  padding: 2rem 0;
}

.sectionHeader {
  margin-bottom: 3rem;
}

.sectionTitle {
  font-size: clamp(2.5rem, 4vw, 3.5rem);
  font-weight: 800;
  line-height: 1.1;
  color: white;
  margin-bottom: 1.5rem;
}

.titleHighlight {
  background: linear-gradient(45deg, #3b82f6, #2563eb);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.sectionDescription {
  font-size: 1.1rem;
  line-height: 1.7;
  color: #cbd5e1;
  max-width: 600px;
}

/* Advantages Grid */
.advantagesGrid {
  display: grid;
  gap: 2rem;
  margin-bottom: 3rem;
}

.advantageCard {
  display: flex;
  gap: 1rem;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.advantageCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
  border-color: #60a5fa;
  background: rgba(255, 255, 255, 0.15);
}

.advantageIcon {
  flex-shrink: 0;
  width: 50px;
  height: 50px;
  background: linear-gradient(45deg, #3b82f6, #2563eb);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.2rem;
}

.advantageContent {
  flex: 1;
}

.advantageTitle {
  font-size: 1.2rem;
  font-weight: 700;
  color: white;
  margin-bottom: 0.5rem;
}

.advantageDescription {
  color: #cbd5e1;
  line-height: 1.6;
  font-size: 0.95rem;
}

/* CTA Section */
.ctaSection {
  text-align: left;
}

.ctaButton {
  display: inline-flex;
  align-items: center;
  gap: 0.75rem;
  background: linear-gradient(45deg, #3b82f6, #2563eb);
  color: white;
  padding: 1rem 2rem;
  border-radius: 50px;
  text-decoration: none;
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

.ctaButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
  background: linear-gradient(45deg, #2563eb, #1d4ed8);
}

.ctaButton i {
  transition: transform 0.3s ease;
}

.ctaButton:hover i {
  transform: translateX(3px);
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .whyChooseEurope {
    padding: 3rem 0;
  }
  
  .imageGrid {
    gap: 1rem;
  }
  
  .campusImage,
  .libraryImage {
    height: 250px;
  }
  
  .sectionTitle {
    text-align: center;
  }
  
  .ctaSection {
    text-align: center;
  }
  
  .ctaButton {
    width: 100%;
    max-width: 300px;
    justify-content: center;
  }
  
  .advantageCard {
    padding: 1rem;
  }
  
  .advantageIcon {
    width: 45px;
    height: 45px;
    font-size: 1.1rem;
  }
}
