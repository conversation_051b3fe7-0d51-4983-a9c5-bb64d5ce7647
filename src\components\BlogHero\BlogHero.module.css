/* BlogHero Component Styles */
.heroSection {
  height: calc(100vh - 3.5rem); /* 100vh minus navbar height (56px) */
  background: linear-gradient(135deg, #0a0e1a 0%, #1a1f2e 25%, #2a3441 50%, #1e2a3a 75%, #0f1419 100%);
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  padding: 10px 0;
}

/* Mobile devices */
@media (min-width: 480px) {
  .heroSection {
    height: calc(100vh - 4rem); /* 100vh minus navbar height (64px) */
  }
}

/* Tablet devices */
@media (min-width: 768px) {
  .heroSection {
    height: calc(100vh - 4.5rem); /* 100vh minus navbar height (72px) */
  }
}

/* Desktop devices */
@media (min-width: 1024px) {
  .heroSection {
    height: calc(100vh - 5rem); /* 100vh minus navbar height (80px) */
  }
}

/* Background Effects */
.backgroundEffects {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
  pointer-events: none;
}

/* Floating Particles */
.floatingParticles {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: linear-gradient(45deg, #3b82f6, #60a5fa);
  border-radius: 50%;
  animation: particleFloat var(--duration, 8s) ease-in-out infinite var(--delay, 0s);
  opacity: 0.6;
}

.particle:nth-child(odd) {
  background: linear-gradient(45deg, #8b5cf6, #a78bfa);
}

.particle:nth-child(3n) {
  background: linear-gradient(45deg, #06b6d4, #67e8f9);
}

/* Gradient Orbs */
.gradientOrbs {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.orb1,
.orb2,
.orb3 {
  position: absolute;
  border-radius: 50%;
  filter: blur(60px);
  opacity: 0.3;
  animation: orbFloat 15s ease-in-out infinite;
}

.orb1 {
  width: 300px;
  height: 300px;
  background: radial-gradient(circle, #3b82f6 0%, transparent 70%);
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.orb2 {
  width: 400px;
  height: 400px;
  background: radial-gradient(circle, #8b5cf6 0%, transparent 70%);
  top: 50%;
  right: 10%;
  animation-delay: -5s;
}

.orb3 {
  width: 250px;
  height: 250px;
  background: radial-gradient(circle, #06b6d4 0%, transparent 70%);
  bottom: 20%;
  left: 30%;
  animation-delay: -10s;
}

/* Animated Grid */
.animatedGrid {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0.1;
}

.gridLine {
  position: absolute;
  background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.3), transparent);
  height: 1px;
  width: 100%;
  animation: gridFlow 12s linear infinite;
}

.gridLine:nth-child(odd) {
  top: calc(var(--index, 0) * 5%);
  animation-delay: calc(var(--index, 0) * 0.5s);
}

.gridLine:nth-child(even) {
  bottom: calc(var(--index, 0) * 5%);
  animation-delay: calc(var(--index, 0) * 0.3s);
  animation-direction: reverse;
}

/* Container */
.container {
  max-width: 1600px;
  margin: 0 auto;
  padding: 0 20px;
  position: relative;
  z-index: 2;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* Hero Content */
.heroContent {
  display: flex;
  flex-direction: column;
  gap: 30px;
  text-align: center;
  width: 100%;
  max-width: 1400px;
}

/* Search Section */
.searchSection {
  width: 100%;
  max-width: 1000px;
  margin: 0 auto;
}

.searchContainer {
  display: flex;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.08));
  border-radius: 50px;
  padding: 4px;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.searchInput {
  flex: 1;
  background: transparent;
  border: none;
  outline: none;
  padding: 16px 24px;
  color: white;
  font-size: 1rem;
  font-weight: 500;
}

.searchInput::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

.searchButton {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  border: none;
  border-radius: 50px;
  padding: 16px 20px;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.searchButton:hover {
  transform: scale(1.05);
  box-shadow: 0 8px 20px rgba(59, 130, 246, 0.4);
}

.heroTitle {
  font-size: 4rem;
  font-weight: 900;
  line-height: 1.3;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.titleLine1 {
  background: linear-gradient(135deg, #ffffff 0%, #e2e8f0 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.4);
}

.titleLine2 {
  background: linear-gradient(135deg, #60a5fa 0%, #3b82f6 50%, #2563eb 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
}

.titleLine3 {
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 50%, #6d28d9 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 0 4px 8px rgba(139, 92, 246, 0.3);
}

.heroDescription {
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.7;
  max-width: 1000px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Category Tags */
.categoryTags {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  justify-content: center;
}

.categoryTag {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  color: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 10px 20px;
  border-radius: 25px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.categoryTag:hover {
  transform: translateY(-2px);
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.2), rgba(59, 130, 246, 0.1));
  border-color: rgba(59, 130, 246, 0.4);
  color: #60a5fa;
  box-shadow: 0 8px 20px rgba(59, 130, 246, 0.3);
}

.categoryTag.active {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  color: white;
  border-color: #3b82f6;
  box-shadow: 0 8px 20px rgba(59, 130, 246, 0.4);
}



/* Hero Visual */
.heroVisual {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

.visualContainer {
  position: relative;
  width: 100%;
  max-width: 400px;
  height: 400px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.blogIcon {
  width: 200px;
  height: 200px;
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 4rem;
  color: white;
  box-shadow: 
    0 20px 60px rgba(59, 130, 246, 0.4),
    0 0 100px rgba(139, 92, 246, 0.3);
  animation: blogIconFloat 6s ease-in-out infinite;
  position: relative;
  z-index: 2;
}

.blogIcon::before {
  content: '';
  position: absolute;
  top: -10px;
  left: -10px;
  right: -10px;
  bottom: -10px;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6, #06b6d4);
  border-radius: 50%;
  z-index: -1;
  animation: iconGlow 3s ease-in-out infinite alternate;
  filter: blur(20px);
  opacity: 0.7;
}

/* Floating Elements */
.floatingElements {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.floatingCard {
  position: absolute;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.08));
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 15px;
  padding: 12px 16px;
  display: flex;
  align-items: center;
  gap: 8px;
  color: white;
  font-size: 0.9rem;
  font-weight: 500;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
  animation: cardFloat 8s ease-in-out infinite;
}

.floatingCard:nth-child(1) {
  top: 20%;
  left: -20%;
  animation-delay: 0s;
}

.floatingCard:nth-child(2) {
  top: 60%;
  right: -20%;
  animation-delay: -2.5s;
}

.floatingCard:nth-child(3) {
  bottom: 20%;
  left: 10%;
  animation-delay: -5s;
}

.floatingCard i {
  color: #60a5fa;
  font-size: 1rem;
}

/* Scroll Indicator */
.scrollIndicator {
  position: absolute;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.9rem;
  animation: scrollBounce 2s ease-in-out infinite;
}

.scrollMouse {
  width: 24px;
  height: 40px;
  border: 2px solid rgba(255, 255, 255, 0.5);
  border-radius: 12px;
  position: relative;
}

.scrollWheel {
  width: 4px;
  height: 8px;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 2px;
  position: absolute;
  top: 6px;
  left: 50%;
  transform: translateX(-50%);
  animation: wheelScroll 2s ease-in-out infinite;
}

/* Animations */
@keyframes particleFloat {
  0%, 100% {
    transform: translateY(0) translateX(0) rotate(0deg);
    opacity: 0.6;
  }
  25% {
    transform: translateY(-20px) translateX(10px) rotate(90deg);
    opacity: 1;
  }
  50% {
    transform: translateY(-40px) translateX(-5px) rotate(180deg);
    opacity: 0.8;
  }
  75% {
    transform: translateY(-20px) translateX(-15px) rotate(270deg);
    opacity: 1;
  }
}

@keyframes orbFloat {
  0%, 100% {
    transform: translateY(0) scale(1);
  }
  50% {
    transform: translateY(-30px) scale(1.1);
  }
}

@keyframes gridFlow {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    transform: translateX(100%);
    opacity: 0;
  }
}

@keyframes blogIconFloat {
  0%, 100% {
    transform: translateY(0) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(5deg);
  }
}

@keyframes iconGlow {
  0% {
    opacity: 0.7;
    transform: scale(1);
  }
  100% {
    opacity: 1;
    transform: scale(1.1);
  }
}

@keyframes cardFloat {
  0%, 100% {
    transform: translateY(0) rotate(0deg);
  }
  33% {
    transform: translateY(-15px) rotate(2deg);
  }
  66% {
    transform: translateY(-5px) rotate(-1deg);
  }
}

@keyframes scrollBounce {
  0%, 100% {
    transform: translateX(-50%) translateY(0);
  }
  50% {
    transform: translateX(-50%) translateY(-10px);
  }
}

@keyframes wheelScroll {
  0% {
    top: 6px;
    opacity: 1;
  }
  50% {
    top: 16px;
    opacity: 0.5;
  }
  100% {
    top: 6px;
    opacity: 1;
  }
}

/* Fade In Animations */
.fadeInUp {
  animation: fadeInUp 1s ease-out forwards;
}

.slideInRight {
  animation: slideInRight 1s ease-out forwards;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Responsive Design */
@media (max-width: 1024px) {
  .container {
    max-width: 1400px;
  }
  
  .heroTitle {
    font-size: 3.5rem;
  }
}

@media (max-width: 768px) {
  .heroSection {
    padding: 100px 0 60px;
  }
  
  .container {
    padding: 0 15px;
  }
  
  .heroTitle {
    font-size: 2.8rem;
  }
  
  .heroDescription {
    font-size: 1.1rem;
  }
  
  .searchSection {
    max-width: 100%;
  }
  
  .searchContainer {
    padding: 3px;
  }
  
  .searchInput {
    padding: 14px 20px;
    font-size: 0.9rem;
  }
  
  .searchButton {
    padding: 14px 18px;
  }
  
  .categoryTags {
    justify-content: center;
    gap: 8px;
  }
  
  .categoryTag {
    padding: 8px 16px;
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .heroTitle {
    font-size: 2.2rem;
  }
  
  .heroDescription {
    font-size: 1rem;
  }
  
  .categoryTag {
    padding: 6px 12px;
    font-size: 0.75rem;
  }
}