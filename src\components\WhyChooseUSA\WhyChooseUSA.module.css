/* Why Choose USA Section */
.whyChooseUSASection {
  position: relative;
  padding: 6rem 0;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #0f172a 100%);
  overflow: hidden;
}

/* Background Elements */
.backgroundElements {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.bgShape1,
.bgShape2,
.bgShape3 {
  position: absolute;
  border-radius: 50%;
  opacity: 0.1;
  animation: float 8s ease-in-out infinite;
}

.bgShape1 {
  width: 400px;
  height: 400px;
  background: linear-gradient(45deg, #1e3a8a, #dc2626);
  top: -10%;
  right: -10%;
  animation-delay: 0s;
}

.bgShape2 {
  width: 300px;
  height: 300px;
  background: linear-gradient(45deg, #dc2626, #1e3a8a);
  bottom: -5%;
  left: -5%;
  animation-delay: 3s;
}

.bgShape3 {
  width: 200px;
  height: 200px;
  background: linear-gradient(45deg, #1e3a8a, #dc2626);
  top: 50%;
  left: 10%;
  animation-delay: 6s;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-30px) rotate(180deg); }
}

/* Container */
.container {
  position: relative;
  z-index: 10;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.contentWrapper {
  display: grid;
  grid-template-columns: 1fr;
  gap: 3rem;
  align-items: center;
}

@media (min-width: 1024px) {
  .contentWrapper {
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
  }
}

/* Left Content */
.leftContent {
  order: 2;
}

@media (min-width: 1024px) {
  .leftContent {
    order: 1;
  }
}

/* Section Header */
.sectionHeader {
  margin-bottom: 3rem;
}

.sectionTitle {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
}

.titlePrefix {
  font-size: 1.2rem;
  font-weight: 500;
  color: #94a3b8;
  text-transform: uppercase;
  letter-spacing: 0.1em;
}

.titleMain {
  font-size: 3.5rem;
  font-weight: 900;
  background: linear-gradient(90deg, #1e3a8a 0%, #dc2626 50%, #1e3a8a 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  /* Fallback for browsers that don't support background-clip */
  color: #dc2626;
  line-height: 1.1;
  filter: drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.3));
}

@media (max-width: 768px) {
  .titleMain {
    font-size: 2.5rem;
  }
}

.sectionDescription {
  font-size: 1.1rem;
  line-height: 1.7;
  color: #cbd5e1;
  max-width: 600px;
}

/* Features Grid */
.featuresGrid {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.featureItem {
  display: flex;
  gap: 1rem;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.featureItem:hover {
  transform: translateY(-5px);
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(30, 58, 138, 0.3);
  box-shadow: 0 10px 30px rgba(30, 58, 138, 0.2);
}

.featureIcon {
  flex-shrink: 0;
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #1e3a8a, #dc2626);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
  box-shadow: 0 8px 25px rgba(30, 58, 138, 0.3);
}

.featureContent {
  flex: 1;
}

.featureTitle {
  font-size: 1.2rem;
  font-weight: 700;
  color: #f1f5f9;
  margin-bottom: 0.5rem;
  line-height: 1.3;
}

.featureDescription {
  font-size: 0.95rem;
  line-height: 1.6;
  color: #94a3b8;
  margin: 0;
}

/* Right Content */
.rightContent {
  order: 1;
  display: flex;
  justify-content: center;
}

@media (min-width: 1024px) {
  .rightContent {
    order: 2;
  }
}

.imageContainer {
  position: relative;
  max-width: 500px;
  width: 100%;
}

.imageWrapper {
  position: relative;
  border-radius: 20px;
  overflow: hidden;
  background: linear-gradient(135deg, rgba(30, 58, 138, 0.1), rgba(220, 38, 38, 0.1));
  padding: 2rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.usaImage {
  width: 100%;
  height: auto;
  max-width: 400px;
  filter: drop-shadow(0 10px 30px rgba(30, 58, 138, 0.3));
  transition: transform 0.3s ease;
}

.imageWrapper:hover .usaImage {
  transform: scale(1.05);
}

.imageGlow {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80%;
  height: 80%;
  background: radial-gradient(circle, rgba(30, 58, 138, 0.2) 0%, transparent 70%);
  border-radius: 50%;
  z-index: -1;
}

/* Floating Stats */
.floatingStats {
  position: absolute;
  bottom: -20px;
  right: -20px;
  z-index: 10;
}

.statCard {
  background: linear-gradient(135deg, #1e3a8a, #dc2626);
  padding: 1.5rem;
  border-radius: 16px;
  text-align: center;
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.statNumber {
  font-size: 2rem;
  font-weight: 900;
  color: white;
  line-height: 1;
  margin-bottom: 0.5rem;
}

.statLabel {
  font-size: 0.9rem;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.fadeInUp {
  animation: fadeInUp 0.8s ease-out both;
}

.slideInUp {
  animation: slideInUp 0.8s ease-out both;
  animation-delay: var(--delay, 0s);
}

.slideInRight {
  animation: slideInRight 0.8s ease-out 0.3s both;
}

/* Responsive Design */
@media (max-width: 768px) {
  .whyChooseUSASection {
    padding: 4rem 0;
  }
  
  .featureItem {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }
  
  .featureIcon {
    align-self: center;
  }
  
  .floatingStats {
    position: static;
    margin-top: 2rem;
    display: flex;
    justify-content: center;
  }
}
