/* USA Universities Hero Section */
.usaHeroSection {
  position: relative;
  min-height: 90vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #0f1419 0%, #1e3a8a 25%, #dc2626 50%, #1e3a8a 75%, #0f1419 100%);
  overflow: hidden;
  padding-top: 4rem; /* Mobile navbar height */
}

@media (min-width: 1024px) {
  .usaHeroSection {
    min-height: 95vh;
    padding-top: 5rem; /* Desktop navbar height */
  }
}

/* Background Elements */
.heroBgElements {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.bgElement1,
.bgElement2,
.bgElement3 {
  position: absolute;
  border-radius: 50%;
  animation: float 6s ease-in-out infinite;
}

.bgElement1 {
  background: rgba(30, 58, 138, 0.15);
}

.bgElement2 {
  background: rgba(220, 38, 38, 0.15);
}

.bgElement3 {
  background: rgba(30, 58, 138, 0.1);
}

.bgElement1 {
  width: 300px;
  height: 300px;
  top: 10%;
  left: -10%;
  animation-delay: 0s;
}

.bgElement2 {
  width: 200px;
  height: 200px;
  top: 60%;
  right: -5%;
  animation-delay: 2s;
}

.bgElement3 {
  width: 150px;
  height: 150px;
  bottom: 20%;
  left: 20%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

/* Gradient Overlay */
.heroGradientOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(15, 20, 25, 0.8) 0%, rgba(30, 58, 138, 0.2) 50%, rgba(220, 38, 38, 0.1) 100%);
  z-index: 2;
}

/* Stars and Stripes Pattern */
.starsStripesPattern {
  position: absolute;
  top: 0;
  right: 0;
  width: 300px;
  height: 200px;
  background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 60 30'%3E%3Cg fill='%23ffffff' fill-opacity='0.03'%3E%3Cpath d='M0 0h60v30H0z'/%3E%3Cpath d='M0 0h60v2H0zM0 4h60v2H0zM0 8h60v2H0zM0 12h60v2H0zM0 16h60v2H0zM0 20h60v2H0zM0 24h60v2H0zM0 28h60v2H0z'/%3E%3Cpath d='M0 0h20v15H0z'/%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Cpath d='M2 2l1 1-1 1-1-1zM6 2l1 1-1 1-1-1zM10 2l1 1-1 1-1-1zM14 2l1 1-1 1-1-1zM18 2l1 1-1 1-1-1z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E") repeat;
  opacity: 0.5;
  z-index: 2;
}

/* Container */
.container {
  position: relative;
  z-index: 10;
  max-width: 800px;
  margin: 0 auto;
  padding: 0 1rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  min-height: calc(90vh - 4rem);
  gap: 1rem;
}

@media (min-width: 1024px) {
  .container {
    min-height: calc(95vh - 5rem);
    padding: 0 2rem;
    gap: 1.5rem;
  }
}

/* Hero Content */
.heroContent {
  text-align: center;
  width: 100%;
}

/* Trust Badge */
.trustBadge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 50px;
  padding: 0.5rem 1rem;
  animation: fadeInUp 0.8s ease-out;
  order: -1;
  align-self: center;
  margin-top: 0.25rem;
}

@media (min-width: 1024px) {
  .trustBadge {
    margin-top: 0.5rem;
  }
}

.trustIcon {
  font-size: 1.2rem;
}

.trustText {
  color: white;
  font-weight: 500;
  font-size: 0.9rem;
}

/* Hero Title */
.heroTitle {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 2rem;
  animation: fadeInUp 0.8s ease-out 0.2s both;
}

.titleMain {
  font-size: 3rem;
  font-weight: 900;
  color: #ffffff;
  text-shadow:
    2px 2px 4px rgba(0, 0, 0, 0.8),
    0 0 30px rgba(30, 58, 138, 0.5),
    0 0 60px rgba(220, 38, 38, 0.3);
  line-height: 1.1;
}

.titleHighlight {
  font-size: 2.5rem;
  font-weight: 700;
  color: #dc2626;
  text-shadow:
    2px 2px 4px rgba(0, 0, 0, 0.8),
    0 0 20px rgba(220, 38, 38, 0.8),
    0 0 40px rgba(30, 58, 138, 0.4);
  line-height: 1.2;
}

.titleLocation {
  font-size: 2rem;
  font-weight: 600;
  color: #e2e8f0;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
  line-height: 1.3;
}

.titleEducation {
  font-size: 1.5rem;
  font-weight: 500;
  color: #cbd5e1;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
  line-height: 1.4;
}

/* Responsive Typography */
@media (max-width: 768px) {
  .titleMain { font-size: 2.5rem; }
  .titleHighlight { font-size: 2rem; }
  .titleLocation { font-size: 1.5rem; }
  .titleEducation { font-size: 1.2rem; }
}

@media (min-width: 1024px) {
  .titleMain { font-size: 4.5rem; }
  .titleHighlight { font-size: 4rem; }
  .titleLocation { font-size: 3rem; }
  .titleEducation { font-size: 2.5rem; }
}

/* CTA Container */
.ctaContainer {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  animation: fadeInUp 0.8s ease-out 0.6s both;
}

@media (min-width: 640px) {
  .ctaContainer {
    flex-direction: row;
    justify-content: center;
    gap: 1.5rem;
  }
}

/* Buttons */
.primaryBtn,
.secondaryBtn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 2rem;
  border-radius: 50px;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.primaryBtn {
  background: linear-gradient(135deg, #1e3a8a 0%, #dc2626 50%, #1e3a8a 100%);
  color: white;
  box-shadow: 0 10px 25px rgba(30, 58, 138, 0.3);
}

.primaryBtn:hover {
  transform: translateY(-2px);
  box-shadow: 0 15px 35px rgba(30, 58, 138, 0.4);
}

.secondaryBtn {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 2px solid rgba(30, 58, 138, 0.4);
  backdrop-filter: blur(10px);
}

.secondaryBtn:hover {
  background: rgba(30, 58, 138, 0.2);
  border-color: rgba(30, 58, 138, 0.6);
  transform: translateY(-2px);
}

/* Button Icons */
.primaryBtn i,
.secondaryBtn i {
  font-size: 1rem;
  transition: transform 0.3s ease;
}

.primaryBtn:hover i,
.secondaryBtn:hover i {
  transform: translateX(3px);
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}
