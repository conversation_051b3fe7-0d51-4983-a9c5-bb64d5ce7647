rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Contact submissions collection
    match /contact_submissions/{document} {
      // Allow anyone to create contact submissions (for the contact form)
      allow create: if true;
      
      // Only authenticated users can read, update, and delete submissions
      allow read, update, delete: if request.auth != null;
    }
    
    // Blog posts collection
    match /blog_posts/{document} {
      // Allow public read access to published blog posts OR authenticated admin users can read all posts
      allow read: if resource == null || resource.data.published == true || request.auth != null;

      // Allow list operations for everyone (needed for queries)
      // The individual document read rules will still apply to filter results
      allow list: if true;

      // Only authenticated admin users can create, update, and delete blog posts
      allow create, update, delete: if request.auth != null;

      // Additional validation for blog post data
      allow write: if request.auth != null &&
        validateBlogPost(request.resource.data);
    }
    
    // Deny all other access by default
    match /{document=**} {
      allow read, write: if false;
    }
  }
  
  // Helper function to validate blog post data structure
  function validateBlogPost(data) {
    return data.keys().hasAll(['title', 'content', 'author', 'category', 'date', 'excerpt', 'image', 'readTime', 'published']) &&
           data.title is string && data.title.size() > 0 &&
           data.content is string && data.content.size() > 0 &&
           data.author is string && data.author.size() > 0 &&
           data.category is string && data.category.size() > 0 &&
           data.date is string &&
           data.excerpt is string && data.excerpt.size() > 0 &&
           data.image is string && data.image.size() > 0 &&
           data.readTime is string && data.readTime.size() > 0 &&
           data.published is bool &&
           (data.featured == null || data.featured is bool) &&
           (data.tags == null || data.tags is list) &&
           (data.createdAt == null || data.createdAt is timestamp) &&
           (data.updatedAt == null || data.updatedAt is timestamp);
  }
}

/*
IMPORTANT: You need to apply these rules in the Firebase Console:

1. Go to https://console.firebase.google.com/
2. Select your project: learn-education-834bb
3. Navigate to Firestore Database
4. Click on "Rules" tab
5. Replace the existing rules with the content above
6. Click "Publish"

These rules ensure:

CONTACT SUBMISSIONS:
- Anyone can submit contact forms (create operation)
- Only authenticated admin users can view, update, and delete submissions

BLOG POSTS:
- Public users can read only published blog posts
- Authenticated admin users can read all posts (including drafts)
- Public users can list/query the collection (needed for queries to work)
- Individual document read permissions still apply during list operations
- Only authenticated admin users can create, update, and delete blog posts
- Data validation ensures proper blog post structure and required fields
- Additional security through field validation (title, content, author, etc.)
- Separate read and list rules for better query performance

SECURITY FEATURES:
- All other operations are denied by default
- Comprehensive data validation for blog posts
- Separation of public and admin access levels
- Protection against malformed data submissions
*/
