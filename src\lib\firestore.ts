import { 
  collection, 
  addDoc, 
  getDocs, 
  getDoc,
  query, 
  orderBy, 
  Timestamp,
  doc,
  deleteDoc,
  updateDoc,
  where,
  limit
} from 'firebase/firestore';
import { db } from './firebase';

// Types for contact form data
export interface ContactFormData {
  name: string;
  email: string;
  phone: string;
  subject: string;
  message: string;
}

export interface ContactSubmission extends ContactFormData {
  id: string;
  timestamp: Timestamp;
  status: 'new' | 'read' | 'replied';
}

// Collection names
const CONTACT_COLLECTION = 'contact_submissions';
const BLOG_COLLECTION = 'blog_posts';

// Types for blog data
export interface BlogPost {
  id?: string;
  title: string;
  excerpt: string;
  content: string;
  author: string;
  date: string;
  category: string;
  readTime: string;
  image: string;
  tags: string[];
  featured?: boolean;
  published: boolean;
  createdAt?: Timestamp;
  updatedAt?: Timestamp;
}

/**
 * Submit a contact form to Firestore
 */
export async function submitContactForm(formData: ContactFormData): Promise<string> {
  try {
    // Validate required fields
    if (!formData.name || !formData.email || !formData.subject || !formData.message) {
      throw new Error('Please fill in all required fields');
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formData.email)) {
      throw new Error('Please enter a valid email address');
    }

    // Add document to Firestore
    const docRef = await addDoc(collection(db, CONTACT_COLLECTION), {
      ...formData,
      timestamp: Timestamp.now(),
      status: 'new'
    });

    return docRef.id;
  } catch (error) {
    console.error('Error submitting contact form:', error);
    throw error;
  }
}

/**
 * Get all contact submissions (admin only)
 */
export async function getContactSubmissions(): Promise<ContactSubmission[]> {
  try {
    const q = query(
      collection(db, CONTACT_COLLECTION),
      orderBy('timestamp', 'desc')
    );
    
    const querySnapshot = await getDocs(q);
    const submissions: ContactSubmission[] = [];
    
    querySnapshot.forEach((doc) => {
      submissions.push({
        id: doc.id,
        ...doc.data()
      } as ContactSubmission);
    });
    
    return submissions;
  } catch (error) {
    console.error('Error fetching contact submissions:', error);
    throw error;
  }
}

/**
 * Update contact submission status (admin only)
 */
export async function updateSubmissionStatus(
  submissionId: string, 
  status: 'new' | 'read' | 'replied'
): Promise<void> {
  try {
    const submissionRef = doc(db, CONTACT_COLLECTION, submissionId);
    await updateDoc(submissionRef, { status });
  } catch (error) {
    console.error('Error updating submission status:', error);
    throw error;
  }
}

/**
 * Delete contact submission (admin only)
 */
export async function deleteSubmission(submissionId: string): Promise<void> {
  try {
    const submissionRef = doc(db, CONTACT_COLLECTION, submissionId);
    await deleteDoc(submissionRef);
  } catch (error) {
    console.error('Error deleting submission:', error);
    throw error;
  }
}

// ============ BLOG FUNCTIONS ============

/**
 * Create a new blog post (admin only)
 */
export async function createBlogPost(blogData: Omit<BlogPost, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
  try {
    // Validate required fields
    if (!blogData.title || !blogData.excerpt || !blogData.content || !blogData.author) {
      throw new Error('Please fill in all required fields');
    }

    const docRef = await addDoc(collection(db, BLOG_COLLECTION), {
      ...blogData,
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now()
    });

    return docRef.id;
  } catch (error) {
    console.error('Error creating blog post:', error);
    throw error;
  }
}

/**
 * Get all blog posts (with optional filters)
 */
export async function getBlogPosts(options?: {
  publishedOnly?: boolean;
  category?: string;
  featured?: boolean;
  limitCount?: number;
}): Promise<BlogPost[]> {
  try {
    // Check if db is initialized
    if (!db) {
      throw new Error('Firestore database not initialized');
    }

    // Use simple query without composite index to avoid Firebase index requirements
    let q = query(
      collection(db, BLOG_COLLECTION),
      orderBy('createdAt', 'desc')
    );

    // Apply limit if specified
    if (options?.limitCount) {
      q = query(q, limit(options.limitCount));
    }

    const querySnapshot = await getDocs(q);
    let posts: BlogPost[] = [];

    if (querySnapshot.empty) {
      console.log('No blog posts found in Firestore collection');
      return [];
    }

    querySnapshot.forEach((doc) => {
      const data = doc.data();
      if (data) {
        posts.push({
          id: doc.id,
          ...data
        } as BlogPost);
      }
    });

    console.log(`Fetched ${posts.length} blog posts from Firestore`);

    // Apply all filtering client-side to avoid composite index requirements
    if (options?.publishedOnly) {
      posts = posts.filter(post => post.published);
      console.log(`After published filter: ${posts.length} posts`);
    }

    if (options?.category) {
      posts = posts.filter(post => post.category === options.category);
      console.log(`After category filter: ${posts.length} posts`);
    }

    if (options?.featured !== undefined) {
      posts = posts.filter(post => post.featured === options.featured);
      console.log(`After featured filter: ${posts.length} posts`);
    }

    return posts;
  } catch (error) {
    console.error('Error fetching blog posts:', error);
    console.error('Error details:', {
      message: error instanceof Error ? error.message : 'Unknown error',
      code: error instanceof Error && 'code' in error ? error.code : 'No code',
      stack: error instanceof Error ? error.stack : 'No stack'
    });
    throw error;
  }
}

/**
 * Get a single blog post by ID
 */
export async function getBlogPost(postId: string): Promise<BlogPost | null> {
  try {
    const docRef = doc(db, BLOG_COLLECTION, postId);
    const docSnap = await getDoc(docRef);
    
    if (docSnap.exists()) {
      return {
        id: docSnap.id,
        ...docSnap.data()
      } as BlogPost;
    }
    
    return null;
  } catch (error) {
    console.error('Error fetching blog post:', error);
    throw error;
  }
}

/**
 * Update a blog post (admin only)
 */
export async function updateBlogPost(postId: string, updates: Partial<BlogPost>): Promise<void> {
  try {
    const postRef = doc(db, BLOG_COLLECTION, postId);
    await updateDoc(postRef, {
      ...updates,
      updatedAt: Timestamp.now()
    });
  } catch (error) {
    console.error('Error updating blog post:', error);
    throw error;
  }
}

/**
 * Delete a blog post (admin only)
 */
export async function deleteBlogPost(postId: string): Promise<void> {
  try {
    const postRef = doc(db, BLOG_COLLECTION, postId);
    await deleteDoc(postRef);
  } catch (error) {
    console.error('Error deleting blog post:', error);
    throw error;
  }
}

/**
 * Get blog categories
 */
export async function getBlogCategories(): Promise<string[]> {
  try {
    const posts = await getBlogPosts({ publishedOnly: true });
    const categories = [...new Set(posts.map(post => post.category))];
    return categories.sort();
  } catch (error) {
    console.error('Error fetching blog categories:', error);
    throw error;
  }
}

/**
 * Get related blog posts based on category and tags
 */
export async function getRelatedBlogPosts(currentPost: BlogPost, limitCount: number = 3): Promise<BlogPost[]> {
  try {
    // Get all published posts
    const allPosts = await getBlogPosts({ publishedOnly: true });

    // Handle case where no posts are returned
    if (!allPosts || allPosts.length === 0) {
      console.log('No blog posts found in database');
      return [];
    }

    // Filter out the current post
    const otherPosts = allPosts.filter(post => post.id !== currentPost.id);

    if (otherPosts.length === 0) {
      console.log('No other posts found besides current post');
      return [];
    }

    // Score posts based on relevance
    const scoredPosts = otherPosts.map(post => {
      let score = 0;

      // Same category gets high score
      if (post.category === currentPost.category) {
        score += 10;
      }

      // Shared tags get points
      if (currentPost.tags && post.tags) {
        const sharedTags = currentPost.tags.filter(tag =>
          post.tags.some(postTag => postTag.toLowerCase() === tag.toLowerCase())
        );
        score += sharedTags.length * 3;
      }

      // Featured posts get a small bonus
      if (post.featured) {
        score += 1;
      }

      return { post, score };
    });

    // Sort by score (highest first) and then by creation date (newest first)
    scoredPosts.sort((a, b) => {
      if (a.score !== b.score) {
        return b.score - a.score;
      }
      // If scores are equal, sort by creation date
      const dateA = a.post.createdAt ? a.post.createdAt.toDate() : new Date(a.post.date);
      const dateB = b.post.createdAt ? b.post.createdAt.toDate() : new Date(b.post.date);
      return dateB.getTime() - dateA.getTime();
    });

    // Return the top posts up to the limit
    return scoredPosts.slice(0, limitCount).map(item => item.post);

  } catch (error) {
    console.error('Error fetching related blog posts:', error);
    // Return empty array instead of throwing to prevent page crashes
    return [];
  }
}
