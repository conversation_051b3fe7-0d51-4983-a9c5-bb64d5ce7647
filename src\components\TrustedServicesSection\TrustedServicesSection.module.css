/* Main Section */
.trustedServicesSection {
  padding: 60px 0;
  background: linear-gradient(135deg, #0f1419 0%, #1a2332 50%, #2b70fa 100%);
  position: relative;
  overflow: hidden;
  min-height: 100vh;
}

/* Enhanced responsive padding */
@media (max-width: 480px) {
  .trustedServicesSection {
    padding: 40px 0;
    min-height: 80vh;
  }
}

@media (min-width: 481px) and (max-width: 768px) {
  .trustedServicesSection {
    padding: 50px 0;
    min-height: 85vh;
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .trustedServicesSection {
    padding: 70px 0;
    min-height: 95vh;
  }
}

@media (min-width: 1025px) {
  .trustedServicesSection {
    padding: 80px 0;
    min-height: 100vh;
  }
}

/* Floating Particles Background */
.particlesContainer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.particle {
  position: absolute;
  background: rgba(59, 130, 246, 0.6);
  border-radius: 50%;
  animation: float 6s ease-in-out infinite;
}

.particle0 {
  width: 4px;
  height: 4px;
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.particle1 {
  width: 6px;
  height: 6px;
  top: 60%;
  left: 80%;
  animation-delay: 1s;
  background: rgba(139, 92, 246, 0.6);
}

.particle2 {
  width: 3px;
  height: 3px;
  top: 80%;
  left: 20%;
  animation-delay: 2s;
  background: rgba(236, 72, 153, 0.6);
}

.particle3 {
  width: 5px;
  height: 5px;
  top: 30%;
  left: 70%;
  animation-delay: 3s;
  background: rgba(34, 197, 94, 0.6);
}

.particle4 {
  width: 4px;
  height: 4px;
  top: 70%;
  left: 50%;
  animation-delay: 4s;
  background: rgba(34, 197, 94, 0.6);
}

/* Animated Wave Background */
.wavesContainer {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 200px;
  z-index: 1;
}

.wave {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 200%;
  height: 100px;
  background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);
  border-radius: 50%;
  animation: wave 8s ease-in-out infinite;
}

.wave1 {
  animation-delay: 0s;
  opacity: 0.3;
}

.wave2 {
  animation-delay: 2s;
  opacity: 0.2;
}

/* Container */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  position: relative;
  z-index: 2;
}

/* Content */
.trustedContent {
  opacity: 1;
  transform: translateY(0);
  transition: all 0.8s ease;
}

.trustedContent.fadeInUp {
  opacity: 1;
  transform: translateY(0);
}

/* Enhanced Trust Section */
.trustSection {
  text-align: center;
  margin-bottom: 80px;
}

/* Trust Badge */
.trustBadge {
  margin-bottom: 60px;
}

.trustBadgeContent {
  display: inline-flex;
  align-items: center;
  gap: 20px;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(15px);
  border: 2px solid rgba(59, 130, 246, 0.3);
  border-radius: 60px;
  padding: 20px 40px;
  color: white;
  position: relative;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(59, 130, 246, 0.2);
  animation: trustBadgeFloat 4s ease-in-out infinite;
}

.trustBadgeContent::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  animation: shimmer 3s ease-in-out infinite;
}

.trustIcon {
  font-size: 24px;
  color: #22c55e;
  animation: iconPulse 2s ease-in-out infinite;
}

.trustInfo {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.trustText {
  font-size: 20px;
  font-weight: 700;
  letter-spacing: 0.5px;
  background: linear-gradient(135deg, #60a5fa, #a78bfa);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.trustStars {
  display: flex;
  gap: 4px;
  justify-content: center;
}

.trustStars i {
  color: #fbbf24;
  font-size: 18px;
  animation: starTwinkle 2s ease-in-out infinite;
}

.trustStars i:nth-child(1) { animation-delay: 0s; }
.trustStars i:nth-child(2) { animation-delay: 0.2s; }
.trustStars i:nth-child(3) { animation-delay: 0.4s; }
.trustStars i:nth-child(4) { animation-delay: 0.6s; }
.trustStars i:nth-child(5) { animation-delay: 0.8s; }

/* Enhanced Stats Grid */
.statsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 30px;
  max-width: 1000px;
  margin: 0 auto;
}

.statCard {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 30px 20px;
  text-align: center;
  position: relative;
  overflow: hidden;
  transition: all 0.4s ease;
  animation: statCardFloat 6s ease-in-out infinite;
}

.statCard1 { animation-delay: 0s; }
.statCard2 { animation-delay: 1s; }
.statCard3 { animation-delay: 2s; }
.statCard4 { animation-delay: 3s; }

.statCard:hover {
  transform: translateY(-10px) scale(1.05);
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(59, 130, 246, 0.5);
  box-shadow: 0 20px 40px rgba(59, 130, 246, 0.3);
}

.statIcon {
  font-size: 32px;
  margin-bottom: 15px;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6, #ec4899);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: iconRotate 4s ease-in-out infinite;
}

.statNumber {
  font-size: 36px;
  font-weight: 900;
  color: white;
  margin-bottom: 10px;
  text-shadow: 0 0 20px rgba(59, 130, 246, 0.5);
  animation: numberGlow 3s ease-in-out infinite;
}

.statLabel {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.statGlow {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100px;
  height: 100px;
  background: radial-gradient(circle, rgba(59, 130, 246, 0.2) 0%, transparent 70%);
  border-radius: 50%;
  opacity: 0;
  animation: glowPulse 4s ease-in-out infinite;
  pointer-events: none;
}

.statCard:hover .statGlow {
  opacity: 1;
}

/* Enhanced Section Header */
.sectionHeader {
  text-align: center;
  margin-bottom: 80px;
}

.titleWrapper {
  position: relative;
  display: inline-block;
  margin-bottom: 30px;
}

.sectionTitle {
  font-size: 3.5rem;
  font-weight: 900;
  color: white;
  margin-bottom: 20px;
  line-height: 1.2;
  perspective: 1000px;
}

.titleWord {
  display: inline-block;
  margin-right: 15px;
  background: linear-gradient(135deg, #60a5fa, #a78bfa, #f472b6);
  background-size: 200% 200%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: gradientShift 3s ease-in-out infinite, titleFloat 4s ease-in-out infinite;
  text-shadow: 0 0 30px rgba(96, 165, 250, 0.5);
}

.titleWord:nth-child(1) { animation-delay: 0s; }
.titleWord:nth-child(2) { animation-delay: 0.3s; }
.titleWord:nth-child(3) { animation-delay: 0.6s; }

.titleHighlight {
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: titleFloat 4s ease-in-out infinite;
  animation-delay: 0.9s;
}

.titleUnderline {
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 4px;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6, #ec4899);
  border-radius: 2px;
  animation: underlineExpand 2s ease-out 0.5s forwards;
}

.sectionDescription {
  font-size: 1.3rem;
  color: rgba(255, 255, 255, 0.9);
  max-width: 700px;
  margin: 0 auto;
  line-height: 1.7;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.highlight {
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700;
  animation: highlightPulse 3s ease-in-out infinite;
}

/* Universities Grid */
.universitiesGrid {
  margin-bottom: 80px;
}

.gridTitle {
  font-size: 32px;
  font-weight: 600;
  color: white;
  text-align: center;
  margin-bottom: 40px;
}

.universityList {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  max-width: 900px;
  margin: 0 auto;
}

.universityItem {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 20px 25px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 15px;
  color: white;
  text-decoration: none;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.universityItem::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s ease;
}

.universityItem:hover::before {
  left: 100%;
}

.universityItem:hover {
  transform: translateY(-5px);
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(59, 130, 246, 0.5);
}

.universityFlag {
  width: 32px;
  height: 24px;
  border-radius: 4px;
  background-size: cover;
  background-position: center;
  flex-shrink: 0;
}

.flagUK {
  background-image: url('https://asvbqmdvplqupbqpigoa.supabase.co/storage/v1/object/public/learneducation/united-kingdom.png');
}

.flagUSA {
  background-image: url('https://asvbqmdvplqupbqpigoa.supabase.co/storage/v1/object/public/learneducation/united-states.png');
}

.flagCanada {
  background-image: url('https://asvbqmdvplqupbqpigoa.supabase.co/storage/v1/object/public/learneducation/canada.png');
}

.flagIreland {
  background-image: url('https://asvbqmdvplqupbqpigoa.supabase.co/storage/v1/object/public/learneducation/ireland.png');
}

.flagAustralia {
  background-image: url('https://asvbqmdvplqupbqpigoa.supabase.co/storage/v1/object/public/learneducation/australia.png');
}

.flagNewZealand {
  background-image: url('https://asvbqmdvplqupbqpigoa.supabase.co/storage/v1/object/public/learneducation/new-zealand.png');
}

.universityName {
  flex: 1;
  font-size: 16px;
  font-weight: 500;
}

.universityItem i {
  font-size: 14px;
  opacity: 0.7;
  transition: all 0.3s ease;
}

.universityItem:hover i {
  opacity: 1;
  transform: translateX(5px);
}

/* Services Grid */
.servicesSection {
  margin-bottom: 80px;
}

.servicesTitle {
  font-size: 3rem;
  font-weight: 700;
  color: white;
  text-align: center;
  margin-bottom: 50px;
  background: linear-gradient(135deg, #60a5fa, #a78bfa, #f472b6);
  background-size: 200% 200%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: gradientShift 3s ease-in-out infinite;
  text-shadow: 0 0 30px rgba(96, 165, 250, 0.5);
}

.servicesGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 30px;
  max-width: 1200px;
  margin: 0 auto;
}

.serviceCard {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 30px;
  text-align: center;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.serviceCard::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s ease;
}

.serviceCard:hover::before {
  left: 100%;
}

.serviceCard:hover {
  transform: translateY(-10px);
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(59, 130, 246, 0.5);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.serviceTitle {
  font-size: 1.5rem;
  font-weight: 600;
  color: white;
  margin: 20px 0 15px 0;
  line-height: 1.3;
}

.serviceDescription {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
  margin: 0;
}

.servicesList {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 30px;
  max-width: 1000px;
  margin: 0 auto;
}

.serviceItem {
  display: flex;
  gap: 20px;
  padding: 30px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.serviceItem::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s ease;
}

.serviceItem:hover::before {
  left: 100%;
}

.serviceItem:hover {
  transform: translateY(-5px);
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(59, 130, 246, 0.5);
}

.serviceIcon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  border-radius: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.serviceIcon i {
  font-size: 24px;
  color: white;
}

.serviceContent h4 {
  font-size: 20px;
  font-weight: 600;
  color: white;
  margin-bottom: 10px;
}

.serviceContent p {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.5;
}

/* Animations */
/* Enhanced Animations */
@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

@keyframes wave {
  0%, 100% {
    transform: translateX(-50%) translateY(0px);
  }
  50% {
    transform: translateX(-50%) translateY(-20px);
  }
}

@keyframes trustBadgeFloat {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-8px);
  }
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

@keyframes iconPulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

@keyframes starTwinkle {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.2);
  }
}

@keyframes statCardFloat {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-5px);
  }
}

@keyframes iconRotate {
  0%, 100% {
    transform: rotateY(0deg);
  }
  50% {
    transform: rotateY(180deg);
  }
}

@keyframes numberGlow {
  0%, 100% {
    text-shadow: 0 0 20px rgba(59, 130, 246, 0.5);
  }
  50% {
    text-shadow: 0 0 30px rgba(59, 130, 246, 0.8), 0 0 40px rgba(139, 92, 246, 0.6);
  }
}

@keyframes glowPulse {
  0%, 100% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.8);
  }
  50% {
    opacity: 0.6;
    transform: translate(-50%, -50%) scale(1.2);
  }
}

@keyframes gradientShift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

@keyframes titleFloat {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-3px);
  }
}

@keyframes underlineExpand {
  from {
    width: 0;
  }
  to {
    width: 80%;
  }
}

@keyframes highlightPulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

/* Enhanced Responsive Design */
@media (max-width: 480px) {
  .container {
    padding: 0 15px;
  }

  .sectionTitle {
    font-size: 2.2rem;
    line-height: 1.2;
  }

  .titleWord {
    margin-right: 8px;
    display: block;
    margin-bottom: 5px;
  }

  .sectionDescription {
    font-size: 1.1rem;
    padding: 0 10px;
  }

  .trustBadgeContent {
    flex-direction: column;
    gap: 15px;
    padding: 20px 25px;
  }

  .trustText {
    font-size: 18px;
  }

  .statsGrid {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }

  .statCard {
    padding: 20px 15px;
  }

  .statNumber {
    font-size: 28px;
  }

  .statIcon {
    font-size: 24px;
  }

  .gridTitle {
    font-size: 24px;
  }

  .universityList {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .universityItem {
    padding: 15px 20px;
  }

  .servicesGrid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .servicesTitle {
    font-size: 2.2rem;
  }

  .serviceCard {
    padding: 25px 20px;
  }

  .serviceTitle {
    font-size: 1.3rem;
  }

  .serviceDescription {
    font-size: 0.95rem;
  }

  .servicesList {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .serviceItem {
    flex-direction: column;
    text-align: center;
    padding: 20px;
  }

  .serviceIcon {
    margin: 0 auto;
  }
}

@media (min-width: 481px) and (max-width: 768px) {
  .container {
    padding: 0 20px;
  }

  .sectionTitle {
    font-size: 2.8rem;
  }

  .titleWord {
    margin-right: 10px;
  }

  .sectionDescription {
    font-size: 1.2rem;
  }

  .trustBadgeContent {
    flex-direction: column;
    gap: 18px;
    padding: 18px 35px;
  }

  .trustText {
    font-size: 16px;
  }

  .statsGrid {
    grid-template-columns: repeat(2, 1fr);
    gap: 25px;
  }

  .statCard {
    padding: 25px 18px;
  }

  .statNumber {
    font-size: 32px;
  }

  .statIcon {
    font-size: 28px;
  }

  .gridTitle {
    font-size: 26px;
  }

  .universityList {
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
  }

  .servicesGrid {
    grid-template-columns: repeat(2, 1fr);
    gap: 25px;
  }

  .servicesTitle {
    font-size: 2.6rem;
  }

  .serviceCard {
    padding: 28px 25px;
  }

  .serviceTitle {
    font-size: 1.4rem;
  }

  .serviceDescription {
    font-size: 0.98rem;
  }

  .servicesList {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }

  .serviceItem {
    padding: 25px;
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .sectionTitle {
    font-size: 3.2rem;
  }

  .statsGrid {
    grid-template-columns: repeat(4, 1fr);
    gap: 25px;
  }

  .universityList {
    grid-template-columns: repeat(2, 1fr);
  }

  .servicesGrid {
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
  }

  .servicesList {
    grid-template-columns: repeat(3, 1fr);
  }
}