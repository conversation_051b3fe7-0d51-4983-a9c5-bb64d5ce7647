/* Partner USA Universities Section */
.partnerSection {
  position: relative;
  padding: 8rem 0;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 25%, #0f172a 50%, #1e293b 75%, #0f172a 100%);
  overflow: hidden;
}

/* Background Elements */
.backgroundElements {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.bgShape1,
.bgShape2,
.bgShape3 {
  position: absolute;
  border-radius: 50%;
  opacity: 0.06;
  animation: float 12s ease-in-out infinite;
}

.bgShape1 {
  width: 600px;
  height: 600px;
  background: linear-gradient(45deg, #1e3a8a, #dc2626);
  top: -20%;
  right: -15%;
  animation-delay: 0s;
}

.bgShape2 {
  width: 400px;
  height: 400px;
  background: linear-gradient(45deg, #dc2626, #1e3a8a);
  bottom: -15%;
  left: -10%;
  animation-delay: 6s;
}

.bgShape3 {
  width: 300px;
  height: 300px;
  background: linear-gradient(45deg, #1e3a8a, #dc2626);
  top: 30%;
  left: 50%;
  animation-delay: 3s;
}

.bgPattern {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'%3E%3Cg fill='%23ffffff' fill-opacity='0.02'%3E%3Cpath d='M0 0h100v100H0z'/%3E%3Cpath d='M20 20h60v60H20z'/%3E%3Cpath d='M40 40h20v20H40z'/%3E%3C/g%3E%3C/svg%3E") repeat;
  opacity: 0.3;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-50px) rotate(180deg); }
}

/* Container */
.container {
  position: relative;
  z-index: 10;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 1rem;
}

/* Section Header */
.sectionHeader {
  text-align: center;
  margin-bottom: 5rem;
  max-width: 900px;
  margin-left: auto;
  margin-right: auto;
}

.sectionTitle {
  font-size: 3.5rem;
  font-weight: 900;
  background: linear-gradient(90deg, #1e3a8a 0%, #dc2626 50%, #1e3a8a 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  color: #dc2626;
  margin-bottom: 1rem;
  line-height: 1.2;
  filter: drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.3));
}

@media (max-width: 768px) {
  .sectionTitle {
    font-size: 2.5rem;
  }
}

.sectionSubtitle {
  font-size: 1.4rem;
  color: #dc2626;
  font-weight: 600;
  margin-bottom: 2rem;
}

.sectionDescription {
  text-align: left;
  color: #cbd5e1;
  line-height: 1.7;
  font-size: 1.1rem;
}

.sectionDescription p {
  margin-bottom: 1.5rem;
}

.brandSignature {
  margin-top: 2rem;
  padding-top: 1rem;
  border-top: 2px solid rgba(30, 58, 138, 0.3);
  color: #1e3a8a;
  font-size: 1.1rem;
}

/* Main Content */
.mainContent {
  display: grid;
  grid-template-columns: 1fr;
  gap: 4rem;
  margin-bottom: 6rem;
}

@media (min-width: 1024px) {
  .mainContent {
    grid-template-columns: 2fr 1fr;
    gap: 5rem;
  }
}

/* Left Content - Universities List */
.leftContent {
  order: 2;
}

@media (min-width: 1024px) {
  .leftContent {
    order: 1;
  }
}

.universitiesSection {
  background: rgba(255, 255, 255, 0.03);
  border-radius: 20px;
  padding: 2.5rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.universitiesTitle {
  font-size: 1.3rem;
  font-weight: 700;
  color: #f1f5f9;
  margin-bottom: 2rem;
  line-height: 1.4;
}

.universitiesList {
  display: grid;
  grid-template-columns: 1fr;
  gap: 0.75rem;
}

@media (min-width: 640px) {
  .universitiesList {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }
}

.universityItem {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  transition: all 0.3s ease;
  border: 1px solid transparent;
}

.universityItem:hover {
  background: rgba(30, 58, 138, 0.1);
  border-color: rgba(30, 58, 138, 0.3);
  transform: translateX(5px);
}

.universityItem i {
  color: #10b981;
  font-size: 0.9rem;
  flex-shrink: 0;
}

.universityItem span {
  color: #e2e8f0;
  font-size: 0.95rem;
  font-weight: 500;
}

/* Right Content */
.rightContent {
  order: 1;
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

@media (min-width: 1024px) {
  .rightContent {
    order: 2;
  }
}

/* Stats Card */
.statsCard {
  background: linear-gradient(135deg, rgba(30, 58, 138, 0.1), rgba(220, 38, 38, 0.1));
  border-radius: 20px;
  padding: 2.5rem;
  text-align: center;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.statNumber {
  font-size: 4rem;
  font-weight: 900;
  background: linear-gradient(135deg, #1e3a8a, #dc2626);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  color: #dc2626;
  line-height: 1;
  margin-bottom: 0.5rem;
}

.statLabel {
  font-size: 1.2rem;
  font-weight: 700;
  color: #f1f5f9;
  margin-bottom: 2rem;
}

.needGuidance h4 {
  font-size: 1.3rem;
  font-weight: 700;
  color: #f1f5f9;
  margin-bottom: 1rem;
}

.needGuidance p {
  color: #cbd5e1;
  margin-bottom: 1.5rem;
  line-height: 1.5;
}

.consultationBtn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: linear-gradient(135deg, #1e3a8a, #dc2626);
  color: white;
  padding: 1rem 2rem;
  border-radius: 30px;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 8px 25px rgba(30, 58, 138, 0.3);
}

.consultationBtn:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 35px rgba(30, 58, 138, 0.4);
}

.consultationBtn i {
  transition: transform 0.3s ease;
}

.consultationBtn:hover i {
  transform: translateX(3px);
}

/* Benefits Grid */
.benefitsGrid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
}

.benefitCard {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 16px;
  padding: 1.5rem;
  text-align: center;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.benefitCard:hover {
  transform: translateY(-5px);
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(30, 58, 138, 0.3);
}

.benefitIcon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #1e3a8a, #dc2626);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1rem;
  color: white;
  font-size: 1.5rem;
}

.benefitTitle {
  font-size: 1.1rem;
  font-weight: 700;
  color: #f1f5f9;
  margin-bottom: 0.5rem;
}

.benefitDescription {
  color: #94a3b8;
  font-size: 0.9rem;
  line-height: 1.4;
  margin: 0;
}

/* CTA Section */
.ctaSection {
  background: linear-gradient(135deg, rgba(30, 58, 138, 0.1), rgba(220, 38, 38, 0.1));
  border-radius: 30px;
  padding: 4rem 2rem;
  text-align: center;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
}

.ctaContent {
  max-width: 800px;
  margin: 0 auto;
}

.ctaTitle {
  font-size: 2.5rem;
  font-weight: 900;
  color: #f1f5f9;
  margin-bottom: 1.5rem;
  line-height: 1.2;
}

@media (max-width: 768px) {
  .ctaTitle {
    font-size: 2rem;
  }
}

.ctaDescription {
  font-size: 1.1rem;
  color: #cbd5e1;
  line-height: 1.7;
  margin-bottom: 2.5rem;
}

.ctaFeatures {
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin-bottom: 3rem;
  flex-wrap: wrap;
}

.ctaFeature {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #10b981;
  font-weight: 600;
}

.ctaFeature i {
  color: #10b981;
}

.ctaStats {
  margin-bottom: 3rem;
}

.ctaStat {
  display: inline-block;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 2rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.ctaStatNumber {
  font-size: 3rem;
  font-weight: 900;
  background: linear-gradient(135deg, #1e3a8a, #dc2626);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  color: #dc2626;
  line-height: 1;
}

.ctaStatLabel {
  font-size: 1.2rem;
  font-weight: 700;
  color: #f1f5f9;
  margin-bottom: 0.5rem;
}

.ctaStatDesc {
  color: #94a3b8;
  font-size: 0.9rem;
}

.ctaButtons {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.primaryCtaBtn {
  display: inline-flex;
  align-items: center;
  gap: 0.75rem;
  background: linear-gradient(135deg, #1e3a8a, #dc2626);
  color: white;
  padding: 1.25rem 3rem;
  border-radius: 40px;
  text-decoration: none;
  font-weight: 700;
  font-size: 1.1rem;
  transition: all 0.3s ease;
  box-shadow: 0 15px 35px rgba(30, 58, 138, 0.3);
}

.primaryCtaBtn:hover {
  transform: translateY(-3px);
  box-shadow: 0 20px 45px rgba(30, 58, 138, 0.4);
}

.primaryCtaBtn i {
  transition: transform 0.3s ease;
}

.primaryCtaBtn:hover i {
  transform: scale(1.1);
}

.ctaDisclaimer {
  color: #94a3b8;
  font-size: 0.9rem;
  font-style: italic;
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInItem {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fadeInUp {
  animation: fadeInUp 0.8s ease-out both;
}

.slideInLeft {
  animation: slideInLeft 0.8s ease-out 0.2s both;
}

.slideInRight {
  animation: slideInRight 0.8s ease-out 0.4s both;
}

.slideInUp {
  animation: slideInUp 0.8s ease-out both;
  animation-delay: var(--delay, 0s);
}

.fadeInItem {
  animation: fadeInItem 0.6s ease-out both;
  animation-delay: var(--delay, 0s);
}

/* Responsive Design */
@media (max-width: 768px) {
  .partnerSection {
    padding: 5rem 0;
  }

  .sectionHeader {
    margin-bottom: 3rem;
  }

  .mainContent {
    gap: 3rem;
    margin-bottom: 4rem;
  }

  .universitiesSection {
    padding: 2rem;
  }

  .statsCard {
    padding: 2rem;
  }

  .ctaSection {
    padding: 3rem 1.5rem;
  }

  .ctaFeatures {
    flex-direction: column;
    gap: 1rem;
  }
}
