/* Partner Universities Section */
.partnerSection {
  position: relative;
  padding: 6rem 0;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 35%, #334155 70%, #475569 100%);
  overflow: hidden;
}

/* Container */
.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 1rem;
}

@media (min-width: 1024px) {
  .container {
    padding: 0 2rem;
  }
}

/* Section Header */
.sectionHeader {
  text-align: center;
  margin-bottom: 4rem;
  animation: fadeInUp 0.8s ease-out;
}

.sectionTitle {
  font-size: 3rem;
  font-weight: 800;
  background: linear-gradient(135deg, #e2e8f0, #3b82f6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 1rem;
  line-height: 1.2;
}

@media (min-width: 768px) {
  .sectionTitle {
    font-size: 4rem;
  }
}

.sectionSubtitle {
  font-size: 1.3rem;
  color: #cbd5e1;
  font-weight: 500;
}

/* Main Content */
.mainContent {
  display: grid;
  grid-template-columns: 1fr;
  gap: 4rem;
  margin-bottom: 5rem;
}

@media (min-width: 1024px) {
  .mainContent {
    grid-template-columns: 1fr 1fr;
    gap: 5rem;
  }
}

/* Left Content */
.leftContent {
  animation: fadeInLeft 0.8s ease-out 0.2s both;
}

.descriptionBox {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 2.5rem;
  margin-bottom: 2rem;
  border: 1px solid rgba(59, 130, 246, 0.2);
}

.description {
  font-size: 1.1rem;
  line-height: 1.7;
  color: #cbd5e1;
  margin-bottom: 1.5rem;
}

.mission {
  font-size: 1.1rem;
  line-height: 1.7;
  color: #e2e8f0;
  margin-bottom: 1.5rem;
  font-style: italic;
}

.brandInfo {
  text-align: right;
}

.brandName {
  font-size: 1.1rem;
  font-weight: 600;
  color: #3b82f6;
}

/* Stats Card */
.statsCard {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  border-radius: 20px;
  padding: 2rem;
  text-align: center;
  color: white;
  box-shadow: 0 20px 40px rgba(59, 130, 246, 0.3);
}

.statNumber {
  font-size: 3rem;
  font-weight: 800;
  margin-bottom: 0.5rem;
}

.statLabel {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 2rem;
}

.needGuidance h4 {
  font-size: 1.3rem;
  margin-bottom: 0.5rem;
}

.needGuidance p {
  margin-bottom: 1.5rem;
  opacity: 0.9;
}

.consultationBtn {
  background: white;
  color: #3b82f6;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  margin: 0 auto;
}

.consultationBtn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(255, 255, 255, 0.3);
}

/* Right Content */
.rightContent {
  animation: fadeInRight 0.8s ease-out 0.4s both;
}

.universitiesContainer {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 2.5rem;
  border: 1px solid rgba(59, 130, 246, 0.2);
}

.listTitle {
  font-size: 1.2rem;
  font-weight: 600;
  color: #e2e8f0;
  margin-bottom: 2rem;
  line-height: 1.4;
}

.universitiesList {
  display: grid;
  grid-template-columns: 1fr;
  gap: 0.75rem;
}

@media (min-width: 768px) {
  .universitiesList {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }
}

.universityItem {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 10px;
  transition: all 0.3s ease;
  animation: fadeInUp 0.6s ease-out both;
  border: 1px solid rgba(59, 130, 246, 0.1);
}

.universityItem:hover {
  background: rgba(255, 255, 255, 0.08);
  transform: translateX(5px);
  border-color: rgba(59, 130, 246, 0.3);
}

.checkmark {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #10b981;
  position: relative;
  flex-shrink: 0;
}

.checkmark::after {
  content: '';
  position: absolute;
  left: 6px;
  top: 3px;
  width: 6px;
  height: 10px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.universityName {
  color: #cbd5e1;
  font-weight: 500;
  font-size: 0.95rem;
}

/* Benefits Section */
.benefitsSection {
  display: grid;
  grid-template-columns: 1fr;
  gap: 2rem;
  margin-bottom: 5rem;
}

@media (min-width: 768px) {
  .benefitsSection {
    grid-template-columns: repeat(3, 1fr);
    gap: 2.5rem;
  }
}

.benefitCard {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 2rem;
  text-align: center;
  border: 1px solid rgba(59, 130, 246, 0.2);
  transition: all 0.3s ease;
  animation: fadeInUp 0.8s ease-out both;
}

.benefitCard:nth-child(2) {
  animation-delay: 0.2s;
}

.benefitCard:nth-child(3) {
  animation-delay: 0.4s;
}

.benefitCard:hover {
  transform: translateY(-5px);
  border-color: rgba(59, 130, 246, 0.4);
  background: rgba(255, 255, 255, 0.08);
}

.benefitIcon {
  width: 60px;
  height: 60px;
  margin: 0 auto 1.5rem;
  border-radius: 15px;
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Globe Icon */
.globe::before {
  content: '';
  width: 30px;
  height: 30px;
  border: 3px solid white;
  border-radius: 50%;
  position: relative;
}

.globe::after {
  content: '';
  position: absolute;
  width: 20px;
  height: 20px;
  border: 2px solid white;
  border-radius: 50%;
  border-left: transparent;
  border-right: transparent;
}

/* Book Icon */
.book::before {
  content: '';
  width: 24px;
  height: 30px;
  background: white;
  border-radius: 2px;
  position: relative;
}

.book::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 16px;
  height: 2px;
  background: #3b82f6;
  box-shadow: 0 -6px 0 #3b82f6, 0 6px 0 #3b82f6;
}

/* Scholarship Icon */
.scholarship::before {
  content: '';
  width: 28px;
  height: 28px;
  background: white;
  border-radius: 50%;
  position: relative;
}

.scholarship::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 16px;
  height: 16px;
  background: #3b82f6;
  border-radius: 50%;
}

.benefitTitle {
  font-size: 1.3rem;
  font-weight: 700;
  color: #e2e8f0;
  margin-bottom: 0.75rem;
}

.benefitDescription {
  color: #cbd5e1;
  line-height: 1.5;
}

/* CTA Section */
.ctaSection {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  border-radius: 25px;
  padding: 3rem;
  display: grid;
  grid-template-columns: 1fr;
  gap: 3rem;
  color: white;
  box-shadow: 0 25px 50px rgba(59, 130, 246, 0.3);
}

@media (min-width: 1024px) {
  .ctaSection {
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    padding: 4rem;
  }
}

.ctaTitle {
  font-size: 2.5rem;
  font-weight: 800;
  margin-bottom: 1.5rem;
  line-height: 1.2;
}

@media (min-width: 768px) {
  .ctaTitle {
    font-size: 3rem;
  }
}

.ctaDescription {
  font-size: 1.1rem;
  line-height: 1.6;
  margin-bottom: 2rem;
  opacity: 0.9;
}

.ctaFeatures {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.ctaFeature {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-weight: 500;
}

.featureIcon {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #10b981;
  position: relative;
  flex-shrink: 0;
}

.featureIcon::after {
  content: '';
  position: absolute;
  left: 6px;
  top: 3px;
  width: 6px;
  height: 10px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

/* CTA Stats */
.ctaStats {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  align-items: center;
}

.ctaStatCard {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 2rem;
  text-align: center;
  border: 1px solid rgba(255, 255, 255, 0.2);
  width: 100%;
  max-width: 300px;
}

.ctaStatNumber {
  font-size: 3rem;
  font-weight: 800;
  margin-bottom: 0.5rem;
}

.ctaStatLabel {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.ctaStatDescription {
  opacity: 0.9;
  font-size: 0.95rem;
}

.ctaButtons {
  text-align: center;
  width: 100%;
}

.primaryCtaBtn {
  background: white;
  color: #3b82f6;
  border: none;
  padding: 1rem 2rem;
  border-radius: 15px;
  font-weight: 700;
  font-size: 1.1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  margin: 0 auto 1rem;
  min-width: 280px;
}

.primaryCtaBtn:hover {
  transform: translateY(-3px);
  box-shadow: 0 15px 35px rgba(255, 255, 255, 0.3);
}

.disclaimer {
  font-size: 0.9rem;
  opacity: 0.8;
  margin: 0;
}

.arrow {
  transition: transform 0.3s ease;
  font-size: 1.2rem;
}

.consultationBtn:hover .arrow,
.primaryCtaBtn:hover .arrow {
  transform: translateX(3px);
}

/* Background Elements */
.backgroundElements {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
  pointer-events: none;
}

.bgShape1,
.bgShape2,
.bgShape3 {
  position: absolute;
  border-radius: 50%;
  background: rgba(59, 130, 246, 0.1);
  animation: floatSlow 12s ease-in-out infinite;
}

.bgShape1 {
  width: 400px;
  height: 400px;
  top: 10%;
  left: -10%;
  animation-delay: 0s;
}

.bgShape2 {
  width: 300px;
  height: 300px;
  top: 50%;
  right: -8%;
  animation-delay: 4s;
}

.bgShape3 {
  width: 200px;
  height: 200px;
  bottom: 20%;
  left: 20%;
  animation-delay: 8s;
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes floatSlow {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-25px) rotate(180deg);
  }
}

/* Mobile Adjustments */
@media (max-width: 768px) {
  .partnerSection {
    padding: 4rem 0;
  }

  .sectionHeader {
    margin-bottom: 3rem;
  }

  .sectionTitle {
    font-size: 2.5rem;
  }

  .mainContent {
    gap: 3rem;
    margin-bottom: 4rem;
  }

  .benefitsSection {
    margin-bottom: 4rem;
  }

  .ctaSection {
    padding: 2rem;
  }

  .ctaTitle {
    font-size: 2rem;
  }

  .primaryCtaBtn {
    min-width: auto;
    width: 100%;
  }
}
