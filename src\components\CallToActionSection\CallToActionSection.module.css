/* Call to Action Section Component Styles */

.callToActionSection {
  padding: 120px 0;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 25%, #334155 50%, #1e293b 75%, #0f172a 100%);
  position: relative;
  overflow: hidden;
}

.callToActionSection::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 25% 25%, rgba(59, 130, 246, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(147, 51, 234, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 50% 50%, rgba(16, 185, 129, 0.1) 0%, transparent 70%);
  pointer-events: none;
  animation: backgroundPulse 20s ease-in-out infinite;
}

/* Background Elements */
.backgroundElements {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
  pointer-events: none;
}

.floatingShape1,
.floatingShape2,
.floatingShape3 {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(147, 51, 234, 0.1));
  animation: floatShape 25s ease-in-out infinite;
}

.floatingShape1 {
  width: 300px;
  height: 300px;
  top: 5%;
  left: 10%;
  animation-delay: 0s;
}

.floatingShape2 {
  width: 200px;
  height: 200px;
  top: 70%;
  right: 15%;
  animation-delay: -8s;
}

.floatingShape3 {
  width: 150px;
  height: 150px;
  top: 40%;
  right: 5%;
  animation-delay: -16s;
}

.gradientOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(59, 130, 246, 0.05) 50%, transparent 70%);
  animation: gradientShift 30s ease-in-out infinite;
}

.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 20px;
  position: relative;
  z-index: 2;
}



/* Main Content */
.mainContent {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 80px;
  align-items: center;
  margin-bottom: 100px;
}

.leftContent {
  padding-right: 20px;
}

.badge {
  display: inline-block;
  background: linear-gradient(135deg, #3b82f6, #2563eb, #1d4ed8);
  color: white;
  padding: 12px 28px;
  border-radius: 30px;
  font-size: 0.9rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 2px;
  margin-bottom: 25px;
  box-shadow: 
    0 8px 25px rgba(59, 130, 246, 0.4),
    0 4px 15px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.mainTitle {
  font-size: 3.2rem;
  font-weight: 900;
  background: linear-gradient(135deg, #ffffff 0%, #e2e8f0 20%, #3b82f6 40%, #60a5fa 60%, #ffffff 80%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-bottom: 30px;
  line-height: 1.2;
}

.mainDescription {
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.8;
  margin-bottom: 40px;
}

/* Features List */
.featuresList {
  margin-bottom: 50px;
}

.featureItem {
  display: flex;
  align-items: flex-start;
  gap: 20px;
  margin-bottom: 30px;
  padding: 25px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.08), rgba(255, 255, 255, 0.04));
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.featureItem:hover {
  transform: translateY(-5px);
  box-shadow: 
    0 15px 40px rgba(0, 0, 0, 0.2),
    0 8px 25px rgba(59, 130, 246, 0.2);
  border-color: rgba(59, 130, 246, 0.3);
}

.featureIcon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
  flex-shrink: 0;
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
  transition: all 0.3s ease;
}

.featureItem:hover .featureIcon {
  transform: scale(1.1) rotate(5deg);
  box-shadow: 0 12px 35px rgba(59, 130, 246, 0.4);
}

.featureContent {
  flex: 1;
}

.featureTitle {
  font-size: 1.3rem;
  font-weight: 700;
  color: white;
  margin-bottom: 10px;
  line-height: 1.3;
}

.featureDescription {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
  margin: 0;
}

/* Stats Container */
.statsContainer {
  display: flex;
  gap: 40px;
  margin-bottom: 40px;
  padding: 30px;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(147, 51, 234, 0.1));
  border-radius: 20px;
  border: 1px solid rgba(59, 130, 246, 0.2);
}

.statItem {
  text-align: center;
  flex: 1;
}

.statNumber {
  font-size: 2.5rem;
  font-weight: 900;
  background: linear-gradient(135deg, #60a5fa, #3b82f6);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  line-height: 1;
  margin-bottom: 8px;
}

.statLabel {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
}

/* Buttons */
.primaryButton {
  display: inline-flex;
  align-items: center;
  gap: 15px;
  padding: 18px 35px;
  background: linear-gradient(135deg, #3b82f6, #2563eb, #1d4ed8);
  color: white;
  text-decoration: none;
  border-radius: 50px;
  font-weight: 700;
  font-size: 1.1rem;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 
    0 12px 30px rgba(59, 130, 246, 0.4),
    0 6px 20px rgba(0, 0, 0, 0.2);
  position: relative;
  overflow: hidden;
}

.primaryButton::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.6s;
}

.primaryButton:hover::before {
  left: 100%;
}

.primaryButton:hover {
  transform: translateY(-3px) scale(1.05);
  box-shadow: 
    0 20px 45px rgba(59, 130, 246, 0.5),
    0 10px 30px rgba(0, 0, 0, 0.3);
}

.secondaryButton {
  display: inline-flex;
  align-items: center;
  gap: 15px;
  padding: 18px 35px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  color: white;
  text-decoration: none;
  border-radius: 50px;
  font-weight: 700;
  font-size: 1.1rem;
  border: 2px solid rgba(255, 255, 255, 0.2);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(20px);
}

.secondaryButton:hover {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.2), rgba(37, 99, 235, 0.1));
  border-color: rgba(59, 130, 246, 0.4);
  transform: translateY(-3px);
  box-shadow: 
    0 15px 35px rgba(0, 0, 0, 0.2),
    0 8px 25px rgba(59, 130, 246, 0.2);
}

.ctaButton {
  display: inline-flex;
  align-items: center;
  gap: 15px;
  padding: 20px 40px;
  background: linear-gradient(135deg, #16a34a, #15803d, #166534);
  color: white;
  text-decoration: none;
  border-radius: 50px;
  font-weight: 700;
  font-size: 1.2rem;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 
    0 12px 30px rgba(22, 163, 74, 0.4),
    0 6px 20px rgba(0, 0, 0, 0.2);
}

.ctaButton:hover {
  transform: translateY(-3px) scale(1.05);
  box-shadow: 
    0 20px 45px rgba(22, 163, 74, 0.5),
    0 10px 30px rgba(0, 0, 0, 0.3);
}

/* Right Content - Image */
.rightContent {
  padding-left: 20px;
}

.imageContainer {
  position: relative;
  border-radius: 30px;
  overflow: hidden;
  box-shadow: 
    0 25px 60px rgba(0, 0, 0, 0.3),
    0 15px 40px rgba(59, 130, 246, 0.2);
}

.heroImage {
  width: 100%;
  height: auto;
  border-radius: 30px;
  transition: transform 0.4s ease;
}

.imageContainer:hover .heroImage {
  transform: scale(1.05);
}

.imageGlow {
  position: absolute;
  top: -50%;
  left: -50%;
  right: -50%;
  bottom: -50%;
  background: radial-gradient(circle, rgba(59, 130, 246, 0.2) 0%, transparent 70%);
  opacity: 0;
  transition: opacity 0.4s ease;
  border-radius: 30px;
}

.imageContainer:hover .imageGlow {
  opacity: 1;
}

/* Bottom Section */
.bottomSection {
  text-align: center;
  padding: 60px 40px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  border-radius: 30px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(30px);
  box-shadow: 
    0 20px 50px rgba(0, 0, 0, 0.3),
    0 10px 30px rgba(59, 130, 246, 0.2);
}

.bottomTitle {
  font-size: 3rem;
  font-weight: 900;
  background: linear-gradient(135deg, #ffffff 0%, #60a5fa 30%, #3b82f6 60%, #ffffff 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-bottom: 25px;
  line-height: 1.1;
}

.bottomDescription {
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.7;
  margin-bottom: 40px;
  max-width: 700px;
  margin-left: auto;
  margin-right: auto;
}

.bottomButtons {
  display: flex;
  gap: 25px;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
}

/* Animations */
.fadeInUp {
  animation: fadeInUp 0.8s ease-out;
}

.slideInLeft {
  animation: slideInLeft 0.8s ease-out;
}

.slideInRight {
  animation: slideInRight 0.8s ease-out;
}

.slideInUp {
  animation: slideInUp 0.6s ease-out var(--delay, 0s) both;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes backgroundPulse {
  0%, 100% {
    transform: scale(1) rotate(0deg);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.1) rotate(180deg);
    opacity: 1;
  }
}

@keyframes floatShape {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.3;
  }
  50% {
    transform: translateY(-40px) rotate(180deg);
    opacity: 0.6;
  }
}

@keyframes gradientShift {
  0%, 100% {
    transform: translateX(0) translateY(0) rotate(0deg);
  }
  25% {
    transform: translateX(30px) translateY(-20px) rotate(90deg);
  }
  50% {
    transform: translateX(-20px) translateY(30px) rotate(180deg);
  }
  75% {
    transform: translateX(20px) translateY(-15px) rotate(270deg);
  }
}

@keyframes titleGlow {
  0%, 100% {
    text-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  }
  50% {
    text-shadow:
      0 0 30px rgba(59, 130, 246, 0.5),
      0 0 60px rgba(59, 130, 246, 0.3);
  }
}

/* Responsive Design */

/* Large Desktop (1440px+) */
@media (min-width: 1440px) {
  .container {
    padding: 0 40px;
  }

  .mainContent {
    gap: 80px;
    max-width: 1300px;
    margin: 0 auto 80px;
  }

  .mainTitle {
    font-size: 3.2rem;
  }

  .mainDescription {
    font-size: 1.3rem;
  }
}

/* Desktop (1024px - 1439px) */
@media (max-width: 1439px) and (min-width: 1024px) {
  .container {
    padding: 0 30px;
  }

  .mainContent {
    gap: 70px;
    max-width: 1200px;
    margin: 0 auto 70px;
  }

  .mainTitle {
    font-size: 2.8rem;
  }

  .mainDescription {
    font-size: 1.2rem;
  }
}

/* Tablet Landscape (768px - 1023px) */
@media (max-width: 1023px) and (min-width: 768px) {
  .callToActionSection {
    padding: 100px 0;
  }

  .container {
    padding: 0 25px;
  }

  .mainContent {
    grid-template-columns: 1fr;
    gap: 60px;
    margin-bottom: 60px;
    text-align: center;
  }

  .leftContent,
  .rightContent {
    padding: 0;
  }

  .mainTitle {
    font-size: 2.5rem;
    line-height: 1.2;
  }

  .mainDescription {
    font-size: 1.15rem;
  }

  .featureItem {
    padding: 25px;
    margin-bottom: 20px;
  }

  .featureIcon {
    width: 55px;
    height: 55px;
    font-size: 1.4rem;
  }

  .featureTitle {
    font-size: 1.2rem;
  }

  .featureDescription {
    font-size: 1rem;
  }

  .statsContainer {
    flex-direction: row;
    gap: 30px;
    padding: 30px;
    max-width: 600px;
    margin: 0 auto 40px;
  }

  .statNumber {
    font-size: 2.2rem;
  }
}

/* Tablet Portrait and Mobile (up to 767px) */
@media (max-width: 767px) {
  .callToActionSection {
    padding: 80px 0;
  }

  .container {
    padding: 0 20px;
  }

  .mainContent {
    grid-template-columns: 1fr;
    gap: 50px;
    margin-bottom: 60px;
    text-align: center;
  }

  .leftContent,
  .rightContent {
    padding: 0;
  }

  .mainTitle {
    font-size: 2.2rem;
    line-height: 1.2;
  }

  .mainDescription {
    font-size: 1.1rem;
  }

  .featureItem {
    padding: 20px;
    margin-bottom: 20px;
  }

  .featureIcon {
    width: 50px;
    height: 50px;
    font-size: 1.3rem;
  }

  .featureTitle {
    font-size: 1.1rem;
  }

  .featureDescription {
    font-size: 0.95rem;
  }

  .statsContainer {
    flex-direction: column;
    gap: 20px;
    padding: 25px;
  }

  .statNumber {
    font-size: 2rem;
  }

  .primaryButton,
  .secondaryButton,
  .ctaButton {
    width: 100%;
    max-width: 300px;
    justify-content: center;
    padding: 16px 30px;
    font-size: 1rem;
    min-height: 48px; /* Touch-friendly */
  }

  .bottomSection {
    padding: 40px 25px;
  }

  .bottomTitle {
    font-size: 2.2rem;
    line-height: 1.2;
  }

  .bottomDescription {
    font-size: 1.1rem;
  }

  .bottomButtons {
    flex-direction: column;
    gap: 15px;
  }
}

/* Mobile (320px - 480px) */
@media (max-width: 480px) {
  .callToActionSection {
    padding: 60px 0;
  }

  .container {
    padding: 0 15px;
  }

  .mainContent {
    gap: 40px;
    margin-bottom: 40px;
    text-align: center;
  }

  .leftContent,
  .rightContent {
    padding: 0;
  }

  .mainTitle {
    font-size: 2rem;
    line-height: 1.2;
  }

  .mainDescription {
    font-size: 1rem;
  }

  .featureItem {
    flex-direction: column;
    text-align: center;
    gap: 15px;
    padding: 20px 15px;
  }

  .featureIcon {
    margin: 0 auto;
    width: 45px;
    height: 45px;
    font-size: 1.2rem;
  }

  .featureTitle {
    font-size: 1rem;
  }

  .featureDescription {
    font-size: 0.9rem;
  }

  .statsContainer {
    padding: 20px;
    flex-direction: column;
    gap: 15px;
  }

  .statNumber {
    font-size: 1.8rem;
  }

  .statLabel {
    font-size: 0.8rem;
  }

  .primaryButton,
  .secondaryButton,
  .ctaButton {
    width: 100%;
    max-width: 280px;
    padding: 16px 24px;
    font-size: 1rem;
    min-height: 48px; /* Touch-friendly */
    justify-content: center;
  }

  .bottomSection {
    padding: 30px 20px;
  }

  .bottomTitle {
    font-size: 1.8rem;
  }

  .bottomDescription {
    font-size: 1rem;
  }

  .bottomButtons {
    flex-direction: column;
    gap: 15px;
    align-items: center;
  }
}
