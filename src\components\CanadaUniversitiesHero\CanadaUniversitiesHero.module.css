/* Canada Universities Hero Section */
.canadaHeroSection {
  position: relative;
  min-height: 80vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #0f1419 0%, #7f1d1d 25%, #dc2626 50%, #ffffff 75%, #0f1419 100%);
  overflow: hidden;
  padding: 3.5rem 0;
}

@media (min-width: 1024px) {
  .canadaHeroSection {
    min-height: 85vh;
    padding: 4.5rem 0;
  }
}

/* Background Elements */
.heroBgElements {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.bgElement1,
.bgElement2,
.bgElement3 {
  position: absolute;
  border-radius: 50%;
  animation: float 6s ease-in-out infinite;
}

.bgElement1 {
  width: 300px;
  height: 300px;
  background: rgba(220, 38, 38, 0.15);
  top: 10%;
  left: -10%;
  animation-delay: 0s;
}

.bgElement2 {
  width: 200px;
  height: 200px;
  background: rgba(255, 255, 255, 0.15);
  top: 60%;
  right: -5%;
  animation-delay: 2s;
}

.bgElement3 {
  width: 150px;
  height: 150px;
  background: rgba(220, 38, 38, 0.1);
  bottom: 20%;
  left: 20%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

/* Gradient Overlay */
.heroGradientOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(15, 20, 25, 0.8) 0%, rgba(220, 38, 38, 0.2) 50%, rgba(255, 255, 255, 0.1) 100%);
  z-index: 2;
}

/* Canadian Pattern */
.canadianPattern {
  position: absolute;
  top: 0;
  right: 0;
  width: 300px;
  height: 200px;
  background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 60 30'%3E%3Cg fill='%23ffffff' fill-opacity='0.03'%3E%3Cpath d='M0 0h60v30H0z'/%3E%3Cpath d='M0 0h30v30H30z'/%3E%3Cg fill='%23dc2626' fill-opacity='0.05'%3E%3Cpath d='M30 0h30v30H60z'/%3E%3C/g%3E%3Cg fill='%23ffffff' fill-opacity='0.04'%3E%3Cpath d='M15 10l3 3-3 3-3-3zM45 10l3 3-3 3-3-3z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E") repeat;
  opacity: 0.5;
  z-index: 2;
}

/* Container */
.container {
  position: relative;
  z-index: 10;
  max-width: 1000px;
  margin: 0 auto;
  padding: 0 1rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: calc(80vh - 7rem);
  gap: 1rem;
}

@media (min-width: 1024px) {
  .container {
    max-width: 1200px;
    min-height: calc(85vh - 9rem);
    padding: 0 2rem;
    gap: 1.5rem;
  }
}

/* Hero Content */
.heroContent {
  text-align: center;
  width: 100%;
}

/* Trust Badge */
.trustBadge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 50px;
  padding: 0.5rem 1rem;
  animation: fadeInUp 0.8s ease-out;
  order: -1;
  align-self: center;
  margin-bottom: 1rem;
}

@media (min-width: 1024px) {
  .trustBadge {
    margin-bottom: 1.5rem;
  }
}

.trustBadge i {
  font-size: 1.2rem;
  color: #dc2626;
}

.trustText {
  color: white;
  font-weight: 500;
  font-size: 0.9rem;
}

/* Hero Title */
.heroTitle {
  display: flex;
  flex-direction: column;
  gap: 0.4rem;
  margin-bottom: 1.75rem;
  animation: fadeInUp 0.8s ease-out 0.2s both;
}

.titleMain {
  font-size: 3rem;
  font-weight: 900;
  color: #ffffff;
  text-shadow:
    2px 2px 4px rgba(0, 0, 0, 0.8),
    0 0 30px rgba(220, 38, 38, 0.5),
    0 0 60px rgba(255, 255, 255, 0.3);
  line-height: 1.1;
}

.titleHighlight {
  font-size: 2.5rem;
  font-weight: 700;
  background: linear-gradient(90deg, #dc2626 0%, #ffffff 50%, #dc2626 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  color: #dc2626;
  line-height: 1.2;
  filter: drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.3));
}

.titleLocation {
  font-size: 2rem;
  font-weight: 600;
  color: #e2e8f0;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
  line-height: 1.3;
}

.titleEducation {
  font-size: 1.5rem;
  font-weight: 500;
  color: #cbd5e1;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
  line-height: 1.4;
}

/* CTA Container */
.ctaContainer {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  align-items: center;
  animation: fadeInUp 0.8s ease-out 0.4s both;
}

@media (min-width: 640px) {
  .ctaContainer {
    flex-direction: row;
    justify-content: center;
    gap: 1.5rem;
  }
}

/* Primary Button */
.primaryBtn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: linear-gradient(135deg, #dc2626, #b91c1c);
  color: white;
  text-decoration: none;
  padding: 1rem 2rem;
  border-radius: 50px;
  font-weight: 700;
  font-size: 1.1rem;
  transition: all 0.3s ease;
  box-shadow: 0 10px 30px rgba(220, 38, 38, 0.3);
  border: none;
  cursor: pointer;
  min-width: 200px;
  justify-content: center;
}

.primaryBtn:hover {
  background: linear-gradient(135deg, #b91c1c, #991b1b);
  transform: translateY(-3px);
  box-shadow: 0 15px 40px rgba(220, 38, 38, 0.4);
}

.primaryBtn i {
  transition: transform 0.3s ease;
}

.primaryBtn:hover i {
  transform: translateX(5px);
}

/* Secondary Button */
.secondaryBtn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  text-decoration: none;
  padding: 1rem 2rem;
  border-radius: 50px;
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  border: 2px solid rgba(255, 255, 255, 0.2);
  min-width: 200px;
  justify-content: center;
}

.secondaryBtn:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(220, 38, 38, 0.5);
  transform: translateY(-2px);
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Design */
@media (min-width: 640px) {
  .titleMain {
    font-size: 3.5rem;
  }

  .titleHighlight {
    font-size: 3rem;
  }

  .titleLocation {
    font-size: 2.25rem;
  }

  .titleEducation {
    font-size: 1.75rem;
  }
}

@media (min-width: 768px) {
  .titleMain {
    font-size: 4rem;
  }

  .titleHighlight {
    font-size: 3.5rem;
  }

  .titleLocation {
    font-size: 2.5rem;
  }

  .titleEducation {
    font-size: 2rem;
  }
}

@media (min-width: 1024px) {
  .titleMain {
    font-size: 5rem;
  }

  .titleHighlight {
    font-size: 4rem;
  }

  .titleLocation {
    font-size: 3rem;
  }

  .titleEducation {
    font-size: 2.25rem;
  }
}

@media (max-width: 480px) {
  .canadaHeroSection {
    min-height: 70vh;
    padding: 2.5rem 0;
  }

  .titleMain {
    font-size: 2.5rem;
  }

  .titleHighlight {
    font-size: 2rem;
  }

  .titleLocation {
    font-size: 1.75rem;
  }

  .titleEducation {
    font-size: 1.25rem;
  }

  .ctaContainer {
    gap: 0.75rem;
  }

  .primaryBtn,
  .secondaryBtn {
    min-width: 180px;
    padding: 0.875rem 1.5rem;
    font-size: 1rem;
  }
}
