/* Blog Management Styles */
.blogManagement {
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
  background: #f8fafc;
  min-height: 100vh;
}

/* Header */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #e2e8f0;
}

.header h2 {
  color: #1e293b;
  font-size: 2rem;
  font-weight: 700;
  margin: 0;
}

.addButton {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 6px -1px rgba(59, 130, 246, 0.3);
}

.addButton:hover:not(:disabled) {
  background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
  transform: translateY(-1px);
  box-shadow: 0 6px 12px -1px rgba(59, 130, 246, 0.4);
}

.addButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Loading */
.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  gap: 1rem;
}

.loadingSpinner {
  color: #3b82f6;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Alerts */
.alert {
  padding: 1rem;
  border-radius: 0.5rem;
  margin-bottom: 1rem;
  font-weight: 500;
}

.alertError {
  background: #fef2f2;
  color: #dc2626;
  border: 1px solid #fecaca;
}

.alertSuccess {
  background: #f0fdf4;
  color: #16a34a;
  border: 1px solid #bbf7d0;
}

/* Form Container */
.formContainer {
  background: white;
  border-radius: 1rem;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  border: 1px solid #e2e8f0;
}

.formHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e2e8f0;
}

.formHeader h3 {
  color: #1e293b;
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
}

.closeButton {
  background: #f1f5f9;
  border: none;
  padding: 0.5rem;
  border-radius: 0.375rem;
  cursor: pointer;
  color: #64748b;
  transition: all 0.2s ease;
}

.closeButton:hover {
  background: #e2e8f0;
  color: #475569;
}

/* Form */
.form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.formGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.formGroup {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.formGroup label {
  font-weight: 600;
  color: #374151;
  font-size: 0.875rem;
}

.formGroup input,
.formGroup select,
.formGroup textarea {
  padding: 0.75rem;
  border: 2px solid #e5e7eb;
  border-radius: 0.5rem;
  font-size: 1rem;
  transition: all 0.2s ease;
  background: white;
}

.formGroup input:focus,
.formGroup select:focus,
.formGroup textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.formGroup textarea {
  resize: vertical;
  min-height: 100px;
  font-family: inherit;
}

/* Checkbox Group */
.checkboxGroup {
  display: flex;
  gap: 2rem;
  margin-top: 1rem;
}

.checkboxLabel {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  cursor: pointer;
  font-weight: 500;
  color: #374151;
}

.checkboxLabel input[type="checkbox"] {
  display: none;
}

.checkmark {
  width: 20px;
  height: 20px;
  border: 2px solid #d1d5db;
  border-radius: 0.25rem;
  position: relative;
  transition: all 0.2s ease;
}

.checkboxLabel input[type="checkbox"]:checked + .checkmark {
  background: #3b82f6;
  border-color: #3b82f6;
}

.checkboxLabel input[type="checkbox"]:checked + .checkmark::after {
  content: '';
  position: absolute;
  left: 6px;
  top: 2px;
  width: 6px;
  height: 10px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

/* Form Actions */
.formActions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 2rem;
  padding-top: 1rem;
  border-top: 1px solid #e2e8f0;
}

.cancelButton {
  padding: 0.75rem 1.5rem;
  border: 2px solid #e5e7eb;
  background: white;
  color: #6b7280;
  border-radius: 0.5rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.cancelButton:hover {
  border-color: #d1d5db;
  color: #374151;
}

.submitButton {
  padding: 0.75rem 1.5rem;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  border: none;
  border-radius: 0.5rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 6px -1px rgba(16, 185, 129, 0.3);
}

.submitButton:hover:not(:disabled) {
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
  transform: translateY(-1px);
  box-shadow: 0 6px 12px -1px rgba(16, 185, 129, 0.4);
}

.submitButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Posts Container */
.postsContainer {
  background: white;
  border-radius: 1rem;
  padding: 2rem;
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  border: 1px solid #e2e8f0;
}

.postsContainer h3 {
  color: #1e293b;
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 1.5rem 0;
}

/* Empty State */
.emptyState {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  color: #6b7280;
  text-align: center;
}

.emptyState svg {
  margin-bottom: 1rem;
  opacity: 0.5;
}

.emptyState p {
  font-size: 1.125rem;
  margin: 0;
}

/* Posts Grid */
.postsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
}

/* Post Card */
.postCard {
  background: white;
  border-radius: 0.75rem;
  overflow: hidden;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
}

.postCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.postImage {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.postImage img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.postCard:hover .postImage img {
  transform: scale(1.05);
}

.postBadges {
  position: absolute;
  top: 1rem;
  right: 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.featuredBadge {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.statusBadge {
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.statusBadge.published {
  background: #dcfce7;
  color: #16a34a;
}

.statusBadge.draft {
  background: #fef3c7;
  color: #d97706;
}

.postContent {
  padding: 1.5rem;
}

.postMeta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.category {
  background: #eff6ff;
  color: #2563eb;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.75rem;
  font-weight: 600;
}

.readTime {
  color: #6b7280;
  font-size: 0.875rem;
}

.postTitle {
  color: #1e293b;
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0 0 0.75rem 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.postExcerpt {
  color: #6b7280;
  font-size: 0.875rem;
  line-height: 1.5;
  margin: 0 0 1.5rem 0;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.postFooter {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 1rem;
  border-top: 1px solid #f1f5f9;
}

.postAuthor {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.postAuthor span:first-child {
  color: #374151;
  font-weight: 500;
  font-size: 0.875rem;
}

.postDate {
  color: #9ca3af;
  font-size: 0.75rem;
}

.postActions {
  display: flex;
  gap: 0.5rem;
}

.editButton,
.deleteButton {
  padding: 0.5rem;
  border: none;
  border-radius: 0.375rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.editButton {
  background: #eff6ff;
  color: #2563eb;
}

.editButton:hover {
  background: #dbeafe;
  color: #1d4ed8;
}

.deleteButton {
  background: #fef2f2;
  color: #dc2626;
}

.deleteButton:hover {
  background: #fee2e2;
  color: #b91c1c;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .blogManagement {
    padding: 1.5rem;
  }
  
  .formGrid {
    grid-template-columns: 1fr;
  }
  
  .postsGrid {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  }
}

@media (max-width: 768px) {
  .blogManagement {
    padding: 1rem;
  }
  
  .header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
  
  .header h2 {
    font-size: 1.5rem;
    text-align: center;
  }
  
  .formContainer {
    padding: 1.5rem;
  }
  
  .formActions {
    flex-direction: column;
  }
  
  .checkboxGroup {
    flex-direction: column;
    gap: 1rem;
  }
  
  .postsGrid {
    grid-template-columns: 1fr;
  }
  
  .postFooter {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }
  
  .postActions {
    align-self: flex-end;
  }
}

@media (max-width: 480px) {
  .blogManagement {
    padding: 0.75rem;
  }
  
  .formContainer,
  .postsContainer {
    padding: 1rem;
  }
  
  .addButton {
    padding: 0.625rem 1.25rem;
    font-size: 0.875rem;
  }
  
  .postImage {
    height: 160px;
  }
  
  .postContent {
    padding: 1rem;
  }
}