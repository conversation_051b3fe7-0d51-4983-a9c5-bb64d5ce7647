/* European Universities CTA Section */
.ctaSection {
  padding: 5rem 0;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
  position: relative;
  overflow: hidden;
}

.ctaSection::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="%23475569" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.1;
  z-index: 1;
}

.ctaSection::after {
  content: '';
  position: absolute;
  bottom: 10%;
  left: 5%;
  width: 150px;
  height: 150px;
  background: linear-gradient(45deg, #3b82f6, #2563eb);
  border-radius: 50%;
  opacity: 0.05;
  animation: float 6s ease-in-out infinite reverse;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  position: relative;
  z-index: 2;
}

/* CTA Content */
.ctaContent {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 4rem;
  align-items: center;
  margin-bottom: 3rem;
}

@media (max-width: 968px) {
  .ctaContent {
    grid-template-columns: 1fr;
    gap: 2rem;
    text-align: center;
  }
}

.textContent {
  padding-right: 2rem;
}

@media (max-width: 968px) {
  .textContent {
    padding-right: 0;
  }
}

.ctaTitle {
  font-size: clamp(2.5rem, 4vw, 3.5rem);
  font-weight: 800;
  color: white;
  margin-bottom: 1.5rem;
  line-height: 1.2;
}

.ctaDescription {
  font-size: 1.2rem;
  color: #cbd5e1;
  line-height: 1.7;
  margin-bottom: 2rem;
}

.featuresList {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

@media (max-width: 968px) {
  .featuresList {
    justify-content: center;
    align-items: flex-start;
    max-width: 300px;
    margin: 0 auto;
  }
}

.featureItem {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1.1rem;
  color: white;
  font-weight: 600;
}

.featureItem i {
  color: #60a5fa;
  font-size: 1rem;
  flex-shrink: 0;
}

/* Stats Content */
.statsContent {
  display: flex;
  justify-content: center;
}

.statCard {
  background: linear-gradient(45deg, #3b82f6, #2563eb);
  color: white;
  padding: 2.5rem;
  border-radius: 20px;
  text-align: center;
  box-shadow: 0 10px 30px rgba(59, 130, 246, 0.3);
  transform: rotate(-2deg);
  transition: transform 0.3s ease;
}

.statCard:hover {
  transform: rotate(0deg) scale(1.05);
}

.statNumber {
  font-size: 3rem;
  font-weight: 800;
  margin-bottom: 0.5rem;
  line-height: 1;
}

.statLabel {
  font-size: 1.3rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.statDescription {
  font-size: 0.95rem;
  opacity: 0.9;
  line-height: 1.4;
}

/* CTA Actions */
.ctaActions {
  text-align: center;
}

.primaryButton {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(45deg, #3b82f6, #2563eb);
  color: white;
  padding: 1.25rem 3rem;
  border-radius: 50px;
  text-decoration: none;
  font-weight: 700;
  font-size: 1.2rem;
  transition: all 0.3s ease;
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
  margin-bottom: 1rem;
}

.primaryButton:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 30px rgba(59, 130, 246, 0.5);
  background: linear-gradient(45deg, #2563eb, #1d4ed8);
}

.disclaimer {
  font-size: 0.9rem;
  color: #94a3b8;
  font-style: italic;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .ctaSection {
    padding: 3rem 0;
  }
  
  .ctaContent {
    margin-bottom: 2rem;
  }
  
  .statCard {
    padding: 2rem;
    transform: rotate(0deg);
  }
  
  .statNumber {
    font-size: 2.5rem;
  }
  
  .primaryButton {
    padding: 1rem 2rem;
    font-size: 1.1rem;
    width: 100%;
    max-width: 350px;
  }
}

/* Animations */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}
