@import "tailwindcss";

/* Smooth Scrolling Base Styles */
html {
  scroll-behavior: smooth;
}

/* <PERSON><PERSON> Smooth Scroll */
html.lenis {
  height: auto;
}

.lenis.lenis-smooth {
  scroll-behavior: auto;
}

.lenis.lenis-smooth [data-lenis-prevent] {
  overscroll-behavior: contain;
}

.lenis.lenis-stopped {
  overflow: hidden;
}

.lenis.lenis-scrolling iframe {
  pointer-events: none;
}

/* Scroll Animation Classes */
.scroll-fade-in {
  opacity: 0;
  transform: translateY(30px);
  transition: opacity 0.6s ease-out, transform 0.6s ease-out;
}

.scroll-fade-in.animate {
  opacity: 1;
  transform: translateY(0);
}

.scroll-slide-up {
  opacity: 0;
  transform: translateY(50px);
  transition: opacity 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94), transform 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.scroll-slide-up.animate {
  opacity: 1;
  transform: translateY(0);
}

.scroll-slide-left {
  opacity: 0;
  transform: translateX(-50px);
  transition: opacity 0.8s ease-out, transform 0.8s ease-out;
}

.scroll-slide-left.animate {
  opacity: 1;
  transform: translateX(0);
}

.scroll-slide-right {
  opacity: 0;
  transform: translateX(50px);
  transition: opacity 0.8s ease-out, transform 0.8s ease-out;
}

.scroll-slide-right.animate {
  opacity: 1;
  transform: translateX(0);
}

.scroll-scale {
  opacity: 0;
  transform: scale(0.9);
  transition: opacity 0.6s ease-out, transform 0.6s ease-out;
}

.scroll-scale.animate {
  opacity: 1;
  transform: scale(1);
}

/* Font Awesome Icons */
@font-face {
  font-family: 'Font Awesome 6 Free';
  font-style: normal;
  font-weight: 900;
  font-display: swap;
  src: url('/assets/fonts/fa-solid-900.woff2') format('woff2'),
       url('/assets/fonts/fa-solid-900.ttf') format('truetype');
}

@font-face {
  font-family: 'Font Awesome 6 Free';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url('/assets/fonts/fa-regular-400.woff2') format('woff2'),
       url('/assets/fonts/fa-regular-400.ttf') format('truetype');
}

@font-face {
  font-family: 'Font Awesome 6 Brands';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url('/assets/fonts/fa-brands-400.woff2') format('woff2'),
       url('/assets/fonts/fa-brands-400.ttf') format('truetype');
}

.fa-solid,
.fas {
  font-family: 'Font Awesome 6 Free';
  font-weight: 900;
}

.fa-regular,
.far {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}

.fa-brands,
.fab {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}

/* Font Awesome icon base styles */
.fa-solid:before,
.fas:before,
.fa-regular:before,
.far:before,
.fa-brands:before,
.fab:before {
  display: inline-block;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
}

/* Specific icon content */
.fa-bars:before { content: "\f0c9"; }
.fa-times:before { content: "\f00d"; }
.fa-chevron-down:before { content: "\f078"; }
.fa-chevron-left:before { content: "\f053"; }
.fa-chevron-right:before { content: "\f054"; }
.fa-phone:before { content: "\f095"; }
.fa-arrow-right:before { content: "\f061"; }
.fa-arrow-left:before { content: "\f060"; }
.fa-check:before { content: "\f00c"; }
.fa-graduation-cap:before { content: "\f19d"; }

/* ProcessSection icons */
.fa-comments:before { content: "\f086"; }
.fa-university:before { content: "\f19c"; }
.fa-file-alt:before { content: "\f15c"; }
.fa-passport:before { content: "\f5ab"; }
.fa-hands-helping:before { content: "\f4c4"; }
.fa-rocket:before { content: "\f135"; }

/* ExpertGuidanceSection icons */
.fa-user-graduate:before { content: "\f501"; }
.fa-bullseye:before { content: "\f140"; }
.fa-gift:before { content: "\f06b"; }

/* AboutHero icons */
.fa-heart:before { content: "\f004"; }
.fa-hands-helping:before { content: "\f4c4"; }

/* OurApproach icons */
.fa-globe:before { content: "\f0ac"; }
.fa-users:before { content: "\f0c0"; }

/* WhyChooseIreland icons */
.fa-location-dot:before { content: "\f3c5"; }

/* FeaturedIrelandUniversities icons */
.fa-book:before { content: "\f02d"; }
.fa-euro-sign:before { content: "\f153"; }

/* PartnerIrelandUniversities icons */
.fa-globe-europe:before { content: "\f7a2"; }
.fa-comments:before { content: "\f086"; }
.fa-calendar-check:before { content: "\f274"; }
.fa-user-tie:before { content: "\f508"; }
.fa-file-alt:before { content: "\f15c"; }

/* CanadaUniversitiesHero icons */
.fa-maple-leaf:before { content: "\f6c6"; }
.fa-passport:before { content: "\f5ab"; }
.fa-globe-americas:before { content: "\f57d"; }
.fa-chevron-right:before { content: "\f054"; }
.fa-play:before { content: "\f04b"; }
.fa-clock:before { content: "\f017"; }
.fa-calendar-alt:before { content: "\f073"; }
.fa-dollar-sign:before { content: "\f155"; }
.fa-language:before { content: "\f1ab"; }

/* WhyChooseCanada icons */
.fa-shield-alt:before { content: "\f3ed"; }

/* GlobalVision icons */
.fa-globe-americas:before { content: "\f57d"; }
.fa-rocket:before { content: "\f135"; }
.fa-graduation-cap:before { content: "\f19d"; }
.fa-university:before { content: "\f19c"; }
.fa-map-marker-alt:before { content: "\f3c5"; }
.fa-earth-americas:before { content: "\f57d"; }
.fa-users-line:before { content: "\e592"; }
.fa-building-columns:before { content: "\f19c"; }

/* OurPassion icons */
.fa-file-alt:before { content: "\f15c"; }
.fa-passport:before { content: "\f5ab"; }
.fa-coins:before { content: "\f51e"; }
.fa-home:before { content: "\f015"; }
.fa-calendar-check:before { content: "\f274"; }
.fa-phone:before { content: "\f095"; }

/* AboutEducation icons */
.fa-book-open:before { content: "\f518"; }
.fa-file-signature:before { content: "\f573"; }
.fa-arrow-right:before { content: "\f061"; }
.fa-star:before { content: "\f005"; }
.fa-trophy:before { content: "\f091"; }

/* WhoWeAre icons */
.fa-calendar-alt:before { content: "\f073"; }
.fa-globe:before { content: "\f0ac"; }
.fa-user-graduate:before { content: "\f501"; }
.fa-lightbulb:before { content: "\f0eb"; }
.fa-handshake:before { content: "\f2b5"; }
.fa-award:before { content: "\f559"; }

/* OurPurpose icons */
.fa-bullseye:before { content: "\f140"; }
.fa-compass:before { content: "\f14e"; }
.fa-eye:before { content: "\f06e"; }
.fa-link:before { content: "\f0c1"; }

/* StudentSuccessSection icons */
.fa-star:before { content: "\f005"; }
.fa-quote-left:before { content: "\f10d"; }
.fa-quote-right:before { content: "\f10e"; }
.fa-check-circle:before { content: "\f058"; }

/* Footer icons */
.fa-building:before { content: "\f1ad"; }
.fa-briefcase:before { content: "\f0b1"; }
.fa-phone:before { content: "\f095"; }
.fa-envelope:before { content: "\f0e0"; }
.fa-chevron-up:before { content: "\f077"; }

/* Social Media Brand icons */
.fa-youtube:before { content: "\f167"; }
.fa-facebook:before { content: "\f09a"; }
.fa-x-twitter:before { content: "\e61b"; }
.fa-linkedin:before { content: "\f08c"; }

/* WhatGuidesUs icons */
.fa-compass:before { content: "\f14e"; }
.fa-user-graduate:before { content: "\f501"; }
.fa-award:before { content: "\f559"; }
.fa-lightbulb:before { content: "\f0eb"; }
.fa-globe:before { content: "\f0ac"; }

/* WhyChooseUs icons */
.fa-star:before { content: "\f005"; }
.fa-user-tie:before { content: "\f508"; }
.fa-handshake:before { content: "\f2b5"; }
.fa-hands-helping:before { content: "\f4c4"; }
.fa-trophy:before { content: "\f091"; }
.fa-arrow-right:before { content: "\f061"; }

/* ServicesHero icons */
.fa-briefcase:before { content: "\f0b1"; }
.fa-graduation-cap:before { content: "\f19d"; }
.fa-university:before { content: "\f19c"; }
.fa-users:before { content: "\f0c0"; }
.fa-chart-line:before { content: "\f201"; }
.fa-search:before { content: "\f002"; }
.fa-file-alt:before { content: "\f15c"; }
.fa-passport:before { content: "\f5ab"; }
.fa-plane:before { content: "\f072"; }
.fa-book:before { content: "\f02d"; }
.fa-calendar:before { content: "\f073"; }

/* PrivacyPolicy icons */
.fa-info-circle:before { content: "\f05a"; }
.fa-cog:before { content: "\f013"; }
.fa-wrench:before { content: "\f0ad"; }
.fa-balance-scale:before { content: "\f24e"; }
.fa-file-text:before { content: "\f15c"; }
.fa-circle:before { content: "\f111"; }
.fa-lock:before { content: "\f023"; }
.fa-list:before { content: "\f03a"; }
.fa-globe:before { content: "\f0ac"; }
.fa-external-link:before { content: "\f08e"; }
.fa-user:before { content: "\f007"; }
.fa-child:before { content: "\f1ae"; }
.fa-clock:before { content: "\f017"; }
.fa-pencil:before { content: "\f303"; }
.fa-envelope:before { content: "\f0e0"; }

/* TrustedServicesSection icons */
.fa-headset:before { content: "\f590"; }
.fa-plane-departure:before { content: "\f5b0"; }



:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

/* ===== BASIC STYLING ===== */

html {
  scroll-padding-top: 80px; /* Account for fixed navbar */
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
  overflow-x: hidden;
  /* Advanced text rendering */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

/* Anchor links */
a[href^="#"] {
  scroll-margin-top: 80px;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Advanced mobile scrolling optimizations */
@media (max-width: 768px) {
  html {
    scroll-padding-top: 70px; /* Adjust for mobile navbar height */
    /* Enhanced mobile scroll performance */
    -webkit-overflow-scrolling: touch;
    scroll-snap-type: y proximity;
  }

  body {
    /* Mobile-specific optimizations */
    -webkit-overflow-scrolling: touch;
    overscroll-behavior-y: contain;
    overscroll-behavior-x: none;
  }

  /* Mobile sections */
  section {
    scroll-margin-top: 70px;
  }
}

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
  html,
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Custom scrollbars */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #3b82f6, #6366f1);
  border-radius: 4px;
  transition: background 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #2563eb, #4f46e5);
}

/* Firefox scrollbar styling */
html {
  scrollbar-width: thin;
  scrollbar-color: #3b82f6 rgba(0, 0, 0, 0.1);
}

/* ===== BASIC OPTIMIZATIONS ===== */

/* CSS Reset for margin and padding */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* Exclude navbar and hero from global transforms to prevent positioning issues */
nav[class*="navbarContainer"],
.navbarContainer,
.navbarContainer *,
[class*="enhancedHeroSection"],
.enhancedHeroSection,
.enhancedHeroSection * {
  -webkit-transform-style: flat !important;
  transform-style: flat !important;
  -webkit-perspective: none !important;
  perspective: none !important;
  -webkit-backface-visibility: visible !important;
  backface-visibility: visible !important;
  -webkit-transform: none !important;
  transform: none !important;
  will-change: auto !important;
  contain: none !important;
  isolation: auto !important;
}

/* Force navbar to be completely independent of any parent transforms */
nav[class*="navbarContainer"] {
  position: sticky !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  z-index: 10000 !important;
  width: 100% !important;
  height: auto !important;
  margin: 0 !important;
  padding: 0 !important;
  border: none !important;
  outline: none !important;
  box-sizing: border-box !important;
}

/* Basic scrolling optimizations */
body {
  -webkit-overflow-scrolling: touch;
  overflow-scrolling: touch;
  overscroll-behavior: contain;
}

/* Maximum performance for sections - exclude hero from transforms */
section:not([class*="enhancedHeroSection"]) {
  contain: layout style paint;
  will-change: transform, opacity;
  /* Smooth section transitions */
  -webkit-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
  scroll-snap-align: start;
  scroll-margin-top: 80px;
}

/* Hero section specific optimizations without transforms */
section[class*="enhancedHeroSection"] {
  contain: none;
  will-change: auto;
  transform: none;
  -webkit-transform: none;
  margin: 0;
  padding: 0;
}

/* Image performance */
img {
  /* Image rendering */
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
}

/* Smooth performance for interactive elements */
button,
a,
input,
textarea {
  will-change: transform;
  -webkit-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
}

/* Optimize animations during scroll */
@media (prefers-reduced-motion: no-preference) {
  /* Basic animations */
  .scroll-animate {
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Fade-in animations */
  .fade-in {
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.6s ease, transform 0.6s ease;
  }

  .fade-in.visible {
    opacity: 1;
    transform: translateY(0);
  }
}

/* ===== BASIC ANIMATIONS ===== */

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.8s ease forwards;
}

/* Basic hover effects */
.hover-lift {
  transition: transform 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-2px);
}

/* ===== RESPONSIVE TYPOGRAPHY SYSTEM ===== */

/* Fluid typography using clamp() for smooth scaling */
:root {
  /* Base font sizes with fluid scaling */
  --font-size-xs: clamp(0.75rem, 0.7rem + 0.25vw, 0.875rem);
  --font-size-sm: clamp(0.875rem, 0.8rem + 0.375vw, 1rem);
  --font-size-base: clamp(1rem, 0.9rem + 0.5vw, 1.125rem);
  --font-size-lg: clamp(1.125rem, 1rem + 0.625vw, 1.25rem);
  --font-size-xl: clamp(1.25rem, 1.1rem + 0.75vw, 1.5rem);
  --font-size-2xl: clamp(1.5rem, 1.3rem + 1vw, 1.875rem);
  --font-size-3xl: clamp(1.875rem, 1.6rem + 1.375vw, 2.25rem);
  --font-size-4xl: clamp(2.25rem, 1.9rem + 1.75vw, 3rem);
  --font-size-5xl: clamp(3rem, 2.5rem + 2.5vw, 4rem);
  --font-size-6xl: clamp(4rem, 3rem + 5vw, 6rem);

  /* Line heights for optimal readability */
  --line-height-tight: 1.1;
  --line-height-snug: 1.2;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.6;
  --line-height-loose: 1.8;

  /* Letter spacing for different text sizes */
  --letter-spacing-tight: -0.025em;
  --letter-spacing-normal: 0;
  --letter-spacing-wide: 0.025em;
  --letter-spacing-wider: 0.05em;
  --letter-spacing-widest: 0.1em;
}

/* Typography utility classes */
.text-responsive-xs { font-size: var(--font-size-xs); }
.text-responsive-sm { font-size: var(--font-size-sm); }
.text-responsive-base { font-size: var(--font-size-base); }
.text-responsive-lg { font-size: var(--font-size-lg); }
.text-responsive-xl { font-size: var(--font-size-xl); }
.text-responsive-2xl { font-size: var(--font-size-2xl); }
.text-responsive-3xl { font-size: var(--font-size-3xl); }
.text-responsive-4xl { font-size: var(--font-size-4xl); }
.text-responsive-5xl { font-size: var(--font-size-5xl); }
.text-responsive-6xl { font-size: var(--font-size-6xl); }

/* Line height utilities */
.leading-tight { line-height: var(--line-height-tight); }
.leading-snug { line-height: var(--line-height-snug); }
.leading-normal { line-height: var(--line-height-normal); }
.leading-relaxed { line-height: var(--line-height-relaxed); }
.leading-loose { line-height: var(--line-height-loose); }

/* Letter spacing utilities */
.tracking-tight { letter-spacing: var(--letter-spacing-tight); }
.tracking-normal { letter-spacing: var(--letter-spacing-normal); }
.tracking-wide { letter-spacing: var(--letter-spacing-wide); }
.tracking-wider { letter-spacing: var(--letter-spacing-wider); }
.tracking-widest { letter-spacing: var(--letter-spacing-widest); }

/* Responsive heading styles */
.heading-hero {
  font-size: var(--font-size-6xl);
  line-height: var(--line-height-tight);
  letter-spacing: var(--letter-spacing-tight);
  font-weight: 900;
}

.heading-section {
  font-size: var(--font-size-5xl);
  line-height: var(--line-height-snug);
  letter-spacing: var(--letter-spacing-tight);
  font-weight: 800;
}

.heading-subsection {
  font-size: var(--font-size-4xl);
  line-height: var(--line-height-snug);
  letter-spacing: var(--letter-spacing-normal);
  font-weight: 700;
}

.heading-card {
  font-size: var(--font-size-2xl);
  line-height: var(--line-height-snug);
  letter-spacing: var(--letter-spacing-normal);
  font-weight: 600;
}

/* Responsive body text styles */
.text-body-large {
  font-size: var(--font-size-lg);
  line-height: var(--line-height-relaxed);
  letter-spacing: var(--letter-spacing-normal);
}

.text-body {
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  letter-spacing: var(--letter-spacing-normal);
}

.text-body-small {
  font-size: var(--font-size-sm);
  line-height: var(--line-height-normal);
  letter-spacing: var(--letter-spacing-normal);
}

/* Touch-friendly button text */
.text-button {
  font-size: var(--font-size-base);
  line-height: var(--line-height-snug);
  letter-spacing: var(--letter-spacing-wide);
  font-weight: 600;
}

/* Responsive text optimization for different devices */
@media (max-width: 480px) {
  :root {
    /* Slightly smaller base sizes for mobile */
    --font-size-base: clamp(0.9rem, 0.85rem + 0.25vw, 1rem);
    --font-size-lg: clamp(1rem, 0.95rem + 0.25vw, 1.125rem);
    --line-height-normal: 1.6; /* Increased for mobile readability */
    --line-height-relaxed: 1.7;
  }
}

@media (min-width: 1440px) {
  :root {
    /* Larger sizes for large screens */
    --font-size-6xl: clamp(4rem, 3rem + 3vw, 5rem);
    --font-size-5xl: clamp(3rem, 2.5rem + 2vw, 3.5rem);
  }
}

/* ===== TOUCH-FRIENDLY INTERACTIONS ===== */

/* Touch target minimum sizes (44px x 44px recommended by WCAG) */
.touch-target {
  min-height: 44px;
  min-width: 44px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.touch-target-large {
  min-height: 48px;
  min-width: 48px;
}

/* Button touch-friendly styles */
.btn-touch {
  min-height: 44px;
  padding: 12px 24px;
  border-radius: 8px;
  transition: all 0.2s ease;
  cursor: pointer;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
  touch-action: manipulation;
}

.btn-touch-large {
  min-height: 48px;
  padding: 16px 32px;
  font-size: 1rem;
}

/* Touch-friendly hover states that work on both mouse and touch */
@media (hover: hover) {
  .btn-touch:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
}

/* Touch feedback for touch devices */
.btn-touch:active {
  transform: translateY(0);
  transition: transform 0.1s ease;
}

/* Card touch interactions */
.card-touch {
  cursor: pointer;
  transition: all 0.3s ease;
  -webkit-tap-highlight-color: transparent;
  touch-action: manipulation;
}

@media (hover: hover) {
  .card-touch:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  }
}

.card-touch:active {
  transform: translateY(-2px);
  transition: transform 0.1s ease;
}

/* Link touch targets */
.link-touch {
  min-height: 44px;
  display: inline-flex;
  align-items: center;
  padding: 8px 12px;
  margin: -8px -12px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
  -webkit-tap-highlight-color: transparent;
  touch-action: manipulation;
}

@media (hover: hover) {
  .link-touch:hover {
    background-color: rgba(0, 0, 0, 0.05);
  }
}

.link-touch:active {
  background-color: rgba(0, 0, 0, 0.1);
}

/* Icon button touch targets */
.icon-btn-touch {
  min-height: 44px;
  min-width: 44px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
  cursor: pointer;
  -webkit-tap-highlight-color: transparent;
  touch-action: manipulation;
}

@media (hover: hover) {
  .icon-btn-touch:hover {
    background-color: rgba(0, 0, 0, 0.1);
    transform: scale(1.1);
  }
}

.icon-btn-touch:active {
  transform: scale(1.05);
  transition: transform 0.1s ease;
}

/* Form input touch improvements */
.input-touch {
  min-height: 44px;
  padding: 12px 16px;
  font-size: 16px; /* Prevents zoom on iOS */
  border-radius: 8px;
  transition: all 0.2s ease;
}

.input-touch:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Dropdown/select touch improvements */
.select-touch {
  min-height: 44px;
  padding: 12px 16px;
  font-size: 16px; /* Prevents zoom on iOS */
  border-radius: 8px;
  cursor: pointer;
  -webkit-appearance: none;
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 12px center;
  background-repeat: no-repeat;
  background-size: 16px;
  padding-right: 40px;
}

/* Touch-friendly spacing utilities */
.touch-spacing-sm { margin: 8px; }
.touch-spacing { margin: 12px; }
.touch-spacing-lg { margin: 16px; }

/* Disable text selection on interactive elements */
.no-select {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* Remove tap highlight on touch devices */
.no-tap-highlight {
  -webkit-tap-highlight-color: transparent;
}

/* Touch-friendly focus styles */
.focus-touch:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
  border-radius: 4px;
}

/* Touch scrolling */
.touch-scroll {
  -webkit-overflow-scrolling: touch;
}

/* Global utility classes - keep only truly global styles */
