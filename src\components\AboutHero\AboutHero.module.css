/* About Hero Component Styles */

/* Main hero section */
.aboutHeroSection {
  position: relative;
  min-height: 100vh;
  background: linear-gradient(135deg, #0f1419 0%, #1a2332 50%, #2b70fa 100%);
  overflow: hidden;
  display: flex;
  align-items: center;
}

.heroGradientOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(15, 20, 25, 0.8), rgba(43, 112, 250, 0.3));
  z-index: 2;
}

.heroBgElements {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

/* Floating background elements */
.aboutHeroSection::before {
  content: '';
  position: absolute;
  top: 15%;
  right: 25%;
  width: 80px;
  height: 80px;
  background: linear-gradient(45deg, #2b70fa, #4a90ff);
  border-radius: 50%;
  opacity: 0.1;
  animation: pulse 4s ease-in-out infinite;
}

.aboutHeroSection::after {
  content: '';
  position: absolute;
  bottom: 25%;
  left: 10%;
  width: 60px;
  height: 60px;
  background: linear-gradient(45deg, #2b70fa, #1e5ce8);
  border-radius: 50%;
  opacity: 0.1;
  animation: pulse 6s ease-in-out infinite reverse;
}

/* Container and layout */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 15px;
  position: relative;
  z-index: 3;
}

.row {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -15px;
  align-items: center;
}

.colLg6 {
  flex: 0 0 50%;
  max-width: 50%;
  padding: 0 15px;
}

@media (max-width: 991px) {
  .colLg6 {
    flex: 0 0 100%;
    max-width: 100%;
  }
}

/* Content styling */
.heroContent {
  position: relative;
  z-index: 3;
  padding-top: 20px;
}

.heroTag {
  margin-bottom: 25px;
  animation: fadeInUp 1s ease-out 0.2s both;
}

.heroTagBadge {
  display: inline-block;
  background: rgba(255, 255, 255, 0.1);
  color: #60a5fa;
  padding: 8px 20px;
  border-radius: 25px;
  font-size: 0.9rem;
  font-weight: 600;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.heroTitle {
  font-size: 3.5rem;
  font-weight: 800;
  line-height: 1.15;
  background: linear-gradient(135deg, #ffffff 0%, #e2e8f0 30%, #ffffff 60%, #f1f5f9 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.4);
  margin-bottom: 30px;
  animation: fadeInUp 1s ease-out 0.4s both;
}

.heroTitleHighlight {
  background: linear-gradient(135deg, #60a5fa 0%, #2b70fa 50%, #1e40af 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.heroDescription {
  font-size: 1.2rem;
  line-height: 1.7;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 35px;
  animation: fadeInUp 1s ease-out 0.6s both;
  max-width: 90%;
}

/* Service Features */
.serviceFeatures {
  margin-bottom: 40px;
  animation: fadeInUp 1s ease-out 0.8s both;
}

.featureGrid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 20px;
}

.featureItem {
  display: flex;
  align-items: center;
  gap: 15px;
  background: rgba(255, 255, 255, 0.05);
  padding: 15px 20px;
  border-radius: 15px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.featureItem:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateX(10px);
}

.featureIcon {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, #2b70fa, #60a5fa);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.2rem;
  flex-shrink: 0;
}

.featureItem span {
  color: rgba(255, 255, 255, 0.9);
  font-size: 1rem;
  font-weight: 500;
}

/* CTA Button */
.heroButtons {
  animation: fadeInUp 1s ease-out 1s both;
}

.btnPrimary {
  display: inline-flex;
  align-items: center;
  gap: 10px;
  background: linear-gradient(135deg, #2b70fa 0%, #1e40af 100%);
  color: white;
  padding: 15px 30px;
  border-radius: 50px;
  text-decoration: none;
  font-weight: 600;
  font-size: 1.1rem;
  transition: all 0.3s ease;
  box-shadow: 0 10px 30px rgba(43, 112, 250, 0.3);
}

.btnPrimary:hover {
  transform: translateY(-3px);
  box-shadow: 0 15px 40px rgba(43, 112, 250, 0.4);
  background: linear-gradient(135deg, #1e40af 0%, #2b70fa 100%);
}

.btnPrimary i {
  font-size: 1.2rem;
}

/* Image section */
.heroImgWrapper {
  position: relative;
  padding-top: 20px;
  animation: fadeInRight 1s ease-out 0.3s both;
}

.heroImg {
  position: relative;
  z-index: 2;
}

.heroImageContainer {
  position: relative;
  overflow: hidden;
  border-radius: 30px;
  background: linear-gradient(145deg, #2b70fa, #1e40af);
  padding: 8px;
  box-shadow:
    0 25px 80px rgba(43, 112, 250, 0.4),
    0 10px 40px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  animation: float 6s ease-in-out infinite;
  transform: perspective(1000px) rotateY(-8deg) rotateX(5deg);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.heroImageContainer::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transform: rotate(45deg);
  animation: shimmer 3s ease-in-out infinite;
}

.heroImageContainer img {
  width: 100%;
  height: auto;
  border-radius: 22px;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  filter: brightness(1.1) contrast(1.1) saturate(1.2);
  position: relative;
  z-index: 2;
}

.heroImageContainer:hover {
  transform: perspective(1000px) rotateY(-12deg) rotateX(8deg) translateY(-10px);
  box-shadow:
    0 35px 100px rgba(43, 112, 250, 0.5),
    0 15px 50px rgba(0, 0, 0, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.heroImageContainer:hover img {
  transform: scale(1.02);
  filter: brightness(1.15) contrast(1.15) saturate(1.3);
}

/* Floating SVG */
.floatingSvg {
  position: absolute;
  top: -20px;
  right: -40px;
  z-index: 4;
  animation: floatSvg 8s ease-in-out infinite;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 20px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
}

.svgElement {
  width: 140px;
  height: auto;
  filter: drop-shadow(0 5px 15px rgba(43, 112, 250, 0.3));
  transition: all 0.3s ease;
}

.floatingSvg:hover .svgElement {
  transform: scale(1.1) rotate(5deg);
  filter: drop-shadow(0 8px 25px rgba(43, 112, 250, 0.5));
}

/* Success Rate Badge */
.heroSuccessRate {
  position: absolute;
  bottom: 25px;
  left: 25px;
  z-index: 5;
  animation: fadeInScale 1s ease-out 1.2s both;
}

.heroSuccessRateBadge {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.85));
  padding: 15px 20px;
  border-radius: 20px;
  box-shadow:
    0 15px 40px rgba(43, 112, 250, 0.3),
    0 5px 15px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(25px);
  text-align: center;
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
}

.heroSuccessRateBadge:hover {
  transform: translateY(-3px) scale(1.05);
  box-shadow:
    0 20px 50px rgba(43, 112, 250, 0.4),
    0 8px 25px rgba(0, 0, 0, 0.15);
}

.successRateText {
  display: block;
  font-size: 1.8rem;
  font-weight: 800;
  background: linear-gradient(135deg, #2b70fa, #1e40af);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  line-height: 1;
}

.successRateLabel {
  display: block;
  font-size: 0.75rem;
  color: #555;
  font-weight: 600;
  margin-top: 4px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Stats Badge */
.heroStatsBadge {
  position: absolute;
  bottom: 35px;
  right: 25px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.85));
  padding: 15px 20px;
  border-radius: 20px;
  box-shadow:
    0 15px 40px rgba(43, 112, 250, 0.3),
    0 5px 15px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(25px);
  text-align: center;
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
  animation: fadeInScale 1s ease-out 1.4s both;
}

.heroStatsBadge:hover {
  transform: translateY(-3px) scale(1.05);
  box-shadow:
    0 20px 50px rgba(43, 112, 250, 0.4),
    0 8px 25px rgba(0, 0, 0, 0.15);
}

.statsText {
  display: block;
  font-size: 1.6rem;
  font-weight: 800;
  background: linear-gradient(135deg, #2b70fa, #1e40af);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  line-height: 1;
}

.statsLabel {
  display: block;
  font-size: 0.75rem;
  color: #555;
  font-weight: 600;
  margin-top: 4px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Utility classes */
.pt20 { padding-top: 20px; }
.mb20 { margin-bottom: 20px; }
.mb25 { margin-bottom: 25px; }
.mb30 { margin-bottom: 30px; }
.textWhite { color: white; }
.pRelative { position: relative; }
.dNone { display: none; }
.dLgBlock { display: block; }

@media (max-width: 991px) {
  .dLgBlock { display: none; }
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.8) translateY(20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes float {
  0%, 100% {
    transform: perspective(1000px) rotateY(-8deg) rotateX(5deg) translateY(0px);
  }
  50% {
    transform: perspective(1000px) rotateY(-8deg) rotateX(5deg) translateY(-15px);
  }
}

@keyframes floatSvg {
  0%, 100% {
    transform: translateY(0px) rotate(0deg) scale(1);
  }
  50% {
    transform: translateY(-20px) rotate(8deg) scale(1.05);
  }
}

@keyframes shimmer {
  0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
  50% { transform: translateX(100%) translateY(100%) rotate(45deg); }
  100% { transform: translateX(200%) translateY(200%) rotate(45deg); }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 0.1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.2;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .heroTitle {
    font-size: 2.5rem;
  }
  
  .heroDescription {
    font-size: 1.1rem;
    max-width: 100%;
  }
  
  .featureItem {
    padding: 12px 15px;
  }
  
  .featureIcon {
    width: 40px;
    height: 40px;
    font-size: 1rem;
  }
  
  .btnPrimary {
    padding: 12px 25px;
    font-size: 1rem;
  }
  
  .floatingSvg {
    top: -30px;
    right: -20px;
  }
  
  .svgElement {
    width: 80px;
  }
}
