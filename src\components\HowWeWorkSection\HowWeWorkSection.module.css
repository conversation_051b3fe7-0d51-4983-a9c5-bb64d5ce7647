/* How We Work Section Component Styles */

.howWeWorkSection {
  padding: 120px 0;
  background: linear-gradient(135deg, #1e293b 0%, #334155 25%, #475569 50%, #334155 75%, #1e293b 100%);
  position: relative;
  overflow: hidden;
}

.howWeWorkSection::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 30% 30%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 70% 70%, rgba(147, 51, 234, 0.1) 0%, transparent 50%),
    linear-gradient(45deg, transparent 30%, rgba(16, 185, 129, 0.05) 50%, transparent 70%);
  pointer-events: none;
  animation: backgroundFlow 25s ease-in-out infinite;
}

/* Background Elements */
.backgroundElements {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
  pointer-events: none;
}

.floatingShape1,
.floatingShape2,
.floatingShape3 {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(147, 51, 234, 0.1));
  animation: floatShape 20s ease-in-out infinite;
}

.floatingShape1 {
  width: 200px;
  height: 200px;
  top: 10%;
  left: 5%;
  animation-delay: 0s;
}

.floatingShape2 {
  width: 150px;
  height: 150px;
  top: 60%;
  right: 10%;
  animation-delay: -7s;
}

.floatingShape3 {
  width: 100px;
  height: 100px;
  top: 30%;
  right: 20%;
  animation-delay: -14s;
}

.connectingLine {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 2px;
  height: 80%;
  background: linear-gradient(to bottom, transparent, rgba(59, 130, 246, 0.3), transparent);
  transform: translateX(-50%) translateY(-50%);
  animation: lineGlow 3s ease-in-out infinite;
}

.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 20px;
  position: relative;
  z-index: 2;
}

/* Header Section */
.headerSection {
  text-align: center;
  margin-bottom: 100px;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.badge {
  display: inline-block;
  background: linear-gradient(135deg, #3b82f6, #2563eb, #1d4ed8);
  color: white;
  padding: 12px 28px;
  border-radius: 30px;
  font-size: 0.9rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 2px;
  margin-bottom: 25px;
  box-shadow: 
    0 8px 25px rgba(59, 130, 246, 0.4),
    0 4px 15px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.mainTitle {
  font-size: 3.8rem;
  font-weight: 900;
  background: linear-gradient(135deg, #ffffff 0%, #e2e8f0 20%, #3b82f6 40%, #60a5fa 60%, #ffffff 80%, #f1f5f9 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-bottom: 30px;
  line-height: 1.1;
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  animation: titleGlow 4s ease-in-out infinite;
}

.description {
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.8;
  margin-bottom: 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Steps Container */
.stepsContainer {
  position: relative;
  max-width: 1200px;
  margin: 0 auto;
}

.stepWrapper {
  display: flex;
  align-items: center;
  margin-bottom: 80px;
  position: relative;
}

.stepWrapper:last-child {
  margin-bottom: 0;
}

.leftAlign {
  justify-content: flex-start;
}

.leftAlign .stepCard {
  margin-left: 0;
  margin-right: 60px;
  max-width: calc(50% - 100px);
}

.rightAlign {
  justify-content: flex-end;
}

.rightAlign .stepCard {
  margin-right: 0;
  margin-left: 60px;
  max-width: calc(50% - 100px);
}

/* Step Number */
.stepNumber {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #1e293b, #334155);
  border: 3px solid rgba(59, 130, 246, 0.3);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.8rem;
  font-weight: 900;
  color: rgba(255, 255, 255, 0.7);
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  z-index: 3;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.stepNumber.active {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  border-color: rgba(59, 130, 246, 0.8);
  color: white;
  transform: scale(1.1);
  box-shadow: 
    0 15px 35px rgba(59, 130, 246, 0.4),
    0 8px 20px rgba(0, 0, 0, 0.3);
}

.numberGlow {
  position: absolute;
  top: -50%;
  left: -50%;
  right: -50%;
  bottom: -50%;
  background: radial-gradient(circle, rgba(59, 130, 246, 0.3) 0%, transparent 70%);
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: 50%;
}

.stepNumber.active .numberGlow {
  opacity: 1;
  animation: glowPulse 2s ease-in-out infinite;
}

/* Step Card */
.stepCard {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  padding: 40px;
  border-radius: 25px;
  position: relative;
  overflow: hidden;
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(30px);
  box-shadow:
    0 15px 40px rgba(0, 0, 0, 0.2),
    0 8px 25px rgba(59, 130, 246, 0.1);
}

.stepCard::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(147, 51, 234, 0.1));
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: 25px;
}

.stepCard.activeCard,
.stepCard:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 
    0 25px 60px rgba(0, 0, 0, 0.3),
    0 15px 35px rgba(59, 130, 246, 0.2);
  border-color: rgba(59, 130, 246, 0.3);
}

.stepCard.activeCard::before,
.stepCard:hover::before {
  opacity: 1;
}

.stepIcon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
  margin-bottom: 25px;
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
  transition: all 0.3s ease;
}

.stepCard:hover .stepIcon {
  transform: scale(1.1) rotate(5deg);
  box-shadow: 0 12px 35px rgba(59, 130, 246, 0.4);
}

.stepContent {
  position: relative;
  z-index: 2;
}

.stepTitle {
  font-size: 1.6rem;
  font-weight: 800;
  background: linear-gradient(135deg, #ffffff, #e2e8f0);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-bottom: 15px;
  line-height: 1.3;
}

.stepDescription {
  font-size: 1.05rem;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.7;
  margin-bottom: 20px;
}

.featuresList {
  list-style: none;
  padding: 0;
  margin: 0;
}

.featureItem {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
  font-size: 0.95rem;
  color: rgba(255, 255, 255, 0.9);
  transition: all 0.3s ease;
}

.featureItem:last-child {
  margin-bottom: 0;
}

.featureItem:hover {
  color: #ffffff;
  transform: translateX(5px);
}

.featureItem i {
  color: #60a5fa;
  font-size: 0.7rem;
  width: 16px;
  height: 16px;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.2), rgba(37, 99, 235, 0.1));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  border: 1px solid rgba(59, 130, 246, 0.3);
}

.stepGlow {
  position: absolute;
  top: -50%;
  left: -50%;
  right: -50%;
  bottom: -50%;
  background: radial-gradient(circle, rgba(59, 130, 246, 0.1) 0%, transparent 70%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.stepCard:hover .stepGlow {
  opacity: 1;
}

/* Step Connector */
.stepConnector {
  position: absolute;
  width: 2px;
  height: 60px;
  background: linear-gradient(to bottom, rgba(59, 130, 246, 0.3), rgba(147, 51, 234, 0.3));
  left: 50%;
  bottom: -70px;
  transform: translateX(-50%);
  z-index: 1;
}

/* Animations */
.fadeInUp {
  animation: fadeInUp 0.8s ease-out;
}

.slideIn {
  animation: slideIn 0.8s ease-out var(--delay, 0s) both;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes backgroundFlow {
  0%, 100% {
    transform: translateX(0) translateY(0) rotate(0deg);
  }
  25% {
    transform: translateX(20px) translateY(-15px) rotate(90deg);
  }
  50% {
    transform: translateX(-10px) translateY(20px) rotate(180deg);
  }
  75% {
    transform: translateX(15px) translateY(-10px) rotate(270deg);
  }
}

@keyframes floatShape {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.3;
  }
  50% {
    transform: translateY(-30px) rotate(180deg);
    opacity: 0.6;
  }
}

@keyframes lineGlow {
  0%, 100% {
    opacity: 0.3;
    transform: translateX(-50%) translateY(-50%) scaleY(1);
  }
  50% {
    opacity: 0.8;
    transform: translateX(-50%) translateY(-50%) scaleY(1.1);
  }
}

@keyframes titleGlow {
  0%, 100% {
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  }
  50% {
    text-shadow:
      0 4px 8px rgba(0, 0, 0, 0.3),
      0 0 20px rgba(59, 130, 246, 0.4),
      0 0 40px rgba(59, 130, 246, 0.2);
  }
}

@keyframes glowPulse {
  0%, 100% {
    transform: scale(1);
    opacity: 0.3;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.8;
  }
}

/* Responsive Design */

/* Large Desktop (1440px+) */
@media (min-width: 1440px) {
  .container {
    padding: 0 40px;
  }

  .stepsContainer {
    max-width: 1300px;
  }

  .stepCard {
    padding: 50px 40px;
  }

  .stepWrapper {
    margin-bottom: 100px;
  }
}

/* Desktop (1024px - 1439px) */
@media (max-width: 1439px) and (min-width: 1024px) {
  .container {
    padding: 0 30px;
  }

  .stepsContainer {
    max-width: 1200px;
  }

  .stepCard {
    padding: 45px 35px;
  }

  .stepWrapper {
    margin-bottom: 80px;
  }
}

/* Tablet Landscape (768px - 1023px) */
@media (max-width: 1023px) and (min-width: 768px) {
  .howWeWorkSection {
    padding: 100px 0;
  }

  .container {
    padding: 0 25px;
  }

  .headerSection {
    margin-bottom: 80px;
  }

  .mainTitle {
    font-size: 3.2rem;
    line-height: 1.1;
  }

  .description {
    font-size: 1.15rem;
  }

  .stepsContainer {
    max-width: 900px;
  }

  .stepWrapper {
    margin-bottom: 70px;
  }

  .stepCard {
    padding: 40px 30px;
    max-width: calc(50% - 80px);
  }

  .leftAlign .stepCard {
    margin-right: 50px;
  }

  .rightAlign .stepCard {
    margin-left: 50px;
  }

  .stepNumber {
    width: 75px;
    height: 75px;
    font-size: 1.6rem;
  }

  .stepIcon {
    width: 55px;
    height: 55px;
    font-size: 1.4rem;
  }

  .stepTitle {
    font-size: 1.4rem;
  }

  .stepDescription {
    font-size: 1.05rem;
  }
}

/* Tablet Portrait and Mobile (up to 767px) */
@media (max-width: 767px) {
  .howWeWorkSection {
    padding: 80px 0;
  }

  .container {
    padding: 0 20px;
  }

  .headerSection {
    margin-bottom: 60px;
  }

  .badge {
    padding: 10px 24px;
    font-size: 0.8rem;
    letter-spacing: 1.5px;
  }

  .mainTitle {
    font-size: 2.8rem;
    line-height: 1.2;
  }

  .description {
    font-size: 1.1rem;
  }

  .stepWrapper {
    flex-direction: column !important;
    margin-bottom: 60px;
    text-align: center;
  }

  .rightAlign {
    flex-direction: column !important;
  }

  .stepNumber {
    margin: 0 0 20px 0;
    width: 70px;
    height: 70px;
    font-size: 1.5rem;
  }

  .stepCard {
    max-width: 100%;
    padding: 30px 25px;
  }

  .stepIcon {
    width: 50px;
    height: 50px;
    font-size: 1.3rem;
    margin: 0 auto 20px;
  }

  .stepTitle {
    font-size: 1.4rem;
  }

  .stepDescription {
    font-size: 1rem;
  }

  .featureItem {
    font-size: 0.9rem;
  }

  .stepConnector {
    display: none;
  }

  .connectingLine {
    display: none;
  }

  .floatingShape1,
  .floatingShape2,
  .floatingShape3 {
    display: none;
  }
}

/* Mobile (320px - 480px) */
@media (max-width: 480px) {
  .howWeWorkSection {
    padding: 60px 0;
  }

  .container {
    padding: 0 15px;
  }

  .headerSection {
    margin-bottom: 50px;
  }

  .badge {
    padding: 8px 20px;
    font-size: 0.75rem;
    letter-spacing: 1px;
  }

  .mainTitle {
    font-size: 2.5rem;
    line-height: 1.2;
  }

  .description {
    font-size: 1rem;
  }

  .stepWrapper {
    margin-bottom: 40px;
    flex-direction: column !important;
    text-align: center;
  }

  .rightAlign {
    flex-direction: column !important;
  }

  .stepNumber {
    width: 60px;
    height: 60px;
    font-size: 1.3rem;
    margin: 0 0 15px 0;
  }

  .stepCard {
    padding: 25px 20px;
    max-width: 100%;
  }

  .stepIcon {
    width: 45px;
    height: 45px;
    font-size: 1.1rem;
    margin: 0 auto 15px;
  }

  .stepTitle {
    font-size: 1.2rem;
  }

  .stepDescription {
    font-size: 0.95rem;
  }

  .featureItem {
    font-size: 0.85rem;
  }

  .stepConnector {
    display: none;
  }

  .connectingLine {
    display: none;
  }

  .floatingShape1,
  .floatingShape2,
  .floatingShape3 {
    display: none;
  }
}
