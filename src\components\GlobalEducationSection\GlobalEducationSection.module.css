/* Global Education Section Styles */
.globalEducationSection {
  padding: 120px 0;
  background: linear-gradient(135deg, #1e293b 0%, #334155 50%, #475569 100%);
  position: relative;
  overflow: hidden;
  min-height: 100vh;
}

/* Background Elements */
.backgroundElements {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.gradientOrb1 {
  position: absolute;
  top: 10%;
  left: 10%;
  width: 300px;
  height: 300px;
  background: radial-gradient(circle, rgba(59, 130, 246, 0.3), transparent);
  border-radius: 50%;
  animation: float 8s ease-in-out infinite;
}

.gradientOrb2 {
  position: absolute;
  top: 60%;
  right: 10%;
  width: 200px;
  height: 200px;
  background: radial-gradient(circle, rgba(139, 92, 246, 0.3), transparent);
  border-radius: 50%;
  animation: float 6s ease-in-out infinite reverse;
}

.gradientOrb3 {
  position: absolute;
  bottom: 20%;
  left: 50%;
  width: 250px;
  height: 250px;
  background: radial-gradient(circle, rgba(236, 72, 153, 0.2), transparent);
  border-radius: 50%;
  animation: float 10s ease-in-out infinite;
}

.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 20px;
  position: relative;
  z-index: 10;
  width: 100%;
  box-sizing: border-box;
}

/* Header Section */
.headerSection {
  text-align: center;
  margin-bottom: 80px;
}

.mainTitle {
  font-size: 3.5rem;
  font-weight: 900;
  color: #f1f5f9;
  margin-bottom: 20px;
  background: linear-gradient(135deg, #60a5fa, #a78bfa, #f472b6);
  background-size: 200% 200%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: gradientShift 3s ease-in-out infinite;
  letter-spacing: -1px;
  line-height: 1.1;
  text-align: center;
  word-break: break-word;
  hyphens: auto;
}

.subtitle {
  font-size: 2rem;
  color: #cbd5e1;
  margin-bottom: 10px;
  font-weight: 700;
  line-height: 1.2;
  text-align: center;
}

.tagline {
  font-size: 1.5rem;
  color: #94a3b8;
  margin-bottom: 30px;
  font-weight: 600;
  line-height: 1.3;
  text-align: center;
}

.description {
  font-size: 1.2rem;
  color: #cbd5e1;
  line-height: 1.7;
  max-width: 800px;
  margin: 0 auto;
  text-align: center;
  padding: 0 20px;
}

/* Stats Section */
.statsSection {
  display: flex;
  justify-content: center;
  gap: 60px;
  margin-bottom: 100px;
  flex-wrap: wrap;
}

.statItem {
  text-align: center;
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  backdrop-filter: blur(20px);
  padding: 30px 25px;
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  min-width: 180px;
}

.statItem:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.statNumber {
  font-size: 3rem;
  font-weight: 900;
  background: linear-gradient(135deg, #60a5fa, #a78bfa);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 10px;
  line-height: 1;
}

.statLabel {
  font-size: 1rem;
  color: #cbd5e1;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
}

/* Countries Grid */
.countriesGrid {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  column-gap: 120px;
  row-gap: 40px;
  margin-bottom: 80px;
  width: 100%;
  box-sizing: border-box;
  padding: 0 20px;
}

/* Ensure grid items don't overflow */
.countriesGrid > * {
  min-width: 0;
  box-sizing: border-box;
  width: 100%;
}

/* Tablet and Desktop: consistent card widths */
@media (min-width: 768px) {
  .countryCard {
    width: 100%;
    max-width: 420px;
    min-width: 350px;
  }
}

.countryCard {
  backdrop-filter: blur(20px);
  padding: 30px;
  border-radius: 24px;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  position: relative;
  overflow: hidden;
  border: 2px solid;
  width: 100%;
  max-width: 400px;
  min-width: 350px;
}

.countryCard:hover {
  transform: translateY(-10px) scale(1.02);
}

/* UK Card - Royal Blue Theme */
.countryCard:nth-child(1) {
  background: linear-gradient(145deg, rgba(30, 58, 138, 0.2), rgba(59, 130, 246, 0.1));
  border-color: #1e3a8a;
  box-shadow: 0 8px 32px rgba(30, 58, 138, 0.3);
}

.countryCard:nth-child(1):hover {
  box-shadow: 0 25px 50px rgba(30, 58, 138, 0.4);
  background: linear-gradient(145deg, rgba(30, 58, 138, 0.3), rgba(59, 130, 246, 0.15));
}

/* USA Card - Patriotic Red Theme */
.countryCard:nth-child(2) {
  background: linear-gradient(145deg, rgba(185, 28, 28, 0.2), rgba(239, 68, 68, 0.1));
  border-color: #b91c1c;
  box-shadow: 0 8px 32px rgba(185, 28, 28, 0.3);
}

.countryCard:nth-child(2):hover {
  box-shadow: 0 25px 50px rgba(185, 28, 28, 0.4);
  background: linear-gradient(145deg, rgba(185, 28, 28, 0.3), rgba(239, 68, 68, 0.15));
}

/* Ireland Card - Emerald Green Theme */
.countryCard:nth-child(3) {
  background: linear-gradient(145deg, rgba(5, 150, 105, 0.2), rgba(34, 197, 94, 0.1));
  border-color: #059669;
  box-shadow: 0 8px 32px rgba(5, 150, 105, 0.3);
}

.countryCard:nth-child(3):hover {
  box-shadow: 0 25px 50px rgba(5, 150, 105, 0.4);
  background: linear-gradient(145deg, rgba(5, 150, 105, 0.3), rgba(34, 197, 94, 0.15));
}

/* Canada Card - Maple Red Theme */
.countryCard:nth-child(4) {
  background: linear-gradient(145deg, rgba(220, 38, 38, 0.2), rgba(248, 113, 113, 0.1));
  border-color: #dc2626;
  box-shadow: 0 8px 32px rgba(220, 38, 38, 0.3);
}

.countryCard:nth-child(4):hover {
  box-shadow: 0 25px 50px rgba(220, 38, 38, 0.4);
  background: linear-gradient(145deg, rgba(220, 38, 38, 0.3), rgba(248, 113, 113, 0.15));
}

/* Europe Card - Purple Theme */
.countryCard:nth-child(5) {
  background: linear-gradient(145deg, rgba(126, 34, 206, 0.2), rgba(168, 85, 247, 0.1));
  border-color: #7e22ce;
  box-shadow: 0 8px 32px rgba(126, 34, 206, 0.3);
}

.countryCard:nth-child(5):hover {
  box-shadow: 0 25px 50px rgba(126, 34, 206, 0.4);
  background: linear-gradient(145deg, rgba(126, 34, 206, 0.3), rgba(168, 85, 247, 0.15));
}

/* Australia Card - Ocean Blue Theme */
.countryCard:nth-child(6) {
  background: linear-gradient(145deg, rgba(7, 89, 133, 0.2), rgba(14, 165, 233, 0.1));
  border-color: #075985;
  box-shadow: 0 8px 32px rgba(7, 89, 133, 0.3);
}

.countryCard:nth-child(6):hover {
  box-shadow: 0 25px 50px rgba(7, 89, 133, 0.4);
  background: linear-gradient(145deg, rgba(7, 89, 133, 0.3), rgba(14, 165, 233, 0.15));
}

/* New Zealand Card - Teal Theme */
.countryCard:nth-child(7) {
  background: linear-gradient(145deg, rgba(13, 148, 136, 0.2), rgba(20, 184, 166, 0.1));
  border-color: #0d9488;
  box-shadow: 0 8px 32px rgba(13, 148, 136, 0.3);
}

.countryCard:nth-child(7):hover {
  box-shadow: 0 25px 50px rgba(13, 148, 136, 0.4);
  background: linear-gradient(145deg, rgba(13, 148, 136, 0.3), rgba(20, 184, 166, 0.15));
}

.popularChoice {
  position: relative;
}

.popularBadge {
  position: absolute;
  top: -1px;
  right: 20px;
  background: linear-gradient(135deg, #1e3a8a, #3b82f6);
  color: white;
  padding: 8px 16px;
  border-radius: 0 0 12px 12px;
  font-size: 0.8rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 4px 15px rgba(30, 58, 138, 0.4);
  animation: pulse 2s ease-in-out infinite;
}

.popularBadge::before {
  content: '⭐';
  margin-right: 5px;
  font-size: 0.9rem;
}

.flagWrapper {
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
}

.flagImage {
  width: 80px;
  height: 50px;
  object-fit: cover;
  border-radius: 8px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.countryName {
  font-size: 1.8rem;
  color: #f1f5f9;
  font-weight: 800;
  margin-bottom: 10px;
  text-align: center;
  line-height: 1.2;
  word-break: break-word;
  hyphens: auto;
}

.universityCount {
  font-size: 1.1rem;
  font-weight: 600;
  text-align: center;
  margin-bottom: 20px;
  padding: 8px 16px;
  border-radius: 20px;
  backdrop-filter: blur(10px);
  line-height: 1.3;
  word-break: break-word;
}

/* UK University Count */
.countryCard:nth-child(1) .universityCount {
  color: #3b82f6;
  background: rgba(30, 58, 138, 0.2);
  border: 1px solid rgba(59, 130, 246, 0.3);
}

/* USA University Count */
.countryCard:nth-child(2) .universityCount {
  color: #ef4444;
  background: rgba(185, 28, 28, 0.2);
  border: 1px solid rgba(239, 68, 68, 0.3);
}

/* Ireland University Count */
.countryCard:nth-child(3) .universityCount {
  color: #22c55e;
  background: rgba(5, 150, 105, 0.2);
  border: 1px solid rgba(34, 197, 94, 0.3);
}

/* Canada University Count */
.countryCard:nth-child(4) .universityCount {
  color: #f87171;
  background: rgba(220, 38, 38, 0.2);
  border: 1px solid rgba(248, 113, 113, 0.3);
}

/* Europe University Count */
.countryCard:nth-child(5) .universityCount {
  color: #a855f7;
  background: rgba(126, 34, 206, 0.2);
  border: 1px solid rgba(168, 85, 247, 0.3);
}

/* Australia University Count */
.countryCard:nth-child(6) .universityCount {
  color: #0ea5e9;
  background: rgba(7, 89, 133, 0.2);
  border: 1px solid rgba(14, 165, 233, 0.3);
}

/* New Zealand University Count */
.countryCard:nth-child(7) .universityCount {
  color: #14b8a6;
  background: rgba(13, 148, 136, 0.2);
  border: 1px solid rgba(20, 184, 166, 0.3);
}

.featuresList {
  list-style: none;
  padding: 0;
  margin-bottom: 25px;
  text-align: left;
  max-width: 100%;
}

.featuresList li {
  color: #cbd5e1;
  margin-bottom: 8px;
  padding-left: 20px;
  position: relative;
  font-size: 0.95rem;
  line-height: 1.4;
  word-break: break-word;
  hyphens: auto;
}

.featuresList li::before {
  content: '✓';
  position: absolute;
  left: 0;
  color: #22c55e;
  font-weight: bold;
}

.learnMoreBtn {
  width: 100%;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
  position: relative;
  overflow: hidden;
}

.learnMoreBtn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.learnMoreBtn:hover::before {
  left: 100%;
}

.learnMoreBtn:hover {
  transform: translateY(-2px);
}

/* UK Button */
.countryCard:nth-child(1) .learnMoreBtn {
  background: linear-gradient(135deg, #1e3a8a, #3b82f6);
  box-shadow: 0 4px 15px rgba(30, 58, 138, 0.3);
}

.countryCard:nth-child(1) .learnMoreBtn:hover {
  box-shadow: 0 8px 25px rgba(30, 58, 138, 0.5);
}

/* USA Button */
.countryCard:nth-child(2) .learnMoreBtn {
  background: linear-gradient(135deg, #b91c1c, #ef4444);
  box-shadow: 0 4px 15px rgba(185, 28, 28, 0.3);
}

.countryCard:nth-child(2) .learnMoreBtn:hover {
  box-shadow: 0 8px 25px rgba(185, 28, 28, 0.5);
}

/* Ireland Button */
.countryCard:nth-child(3) .learnMoreBtn {
  background: linear-gradient(135deg, #059669, #22c55e);
  box-shadow: 0 4px 15px rgba(5, 150, 105, 0.3);
}

.countryCard:nth-child(3) .learnMoreBtn:hover {
  box-shadow: 0 8px 25px rgba(5, 150, 105, 0.5);
}

/* Canada Button */
.countryCard:nth-child(4) .learnMoreBtn {
  background: linear-gradient(135deg, #dc2626, #f87171);
  box-shadow: 0 4px 15px rgba(220, 38, 38, 0.3);
}

.countryCard:nth-child(4) .learnMoreBtn:hover {
  box-shadow: 0 8px 25px rgba(220, 38, 38, 0.5);
}

/* Europe Button */
.countryCard:nth-child(5) .learnMoreBtn {
  background: linear-gradient(135deg, #7e22ce, #a855f7);
  box-shadow: 0 4px 15px rgba(126, 34, 206, 0.3);
}

.countryCard:nth-child(5) .learnMoreBtn:hover {
  box-shadow: 0 8px 25px rgba(126, 34, 206, 0.5);
}

/* Australia Button */
.countryCard:nth-child(6) .learnMoreBtn {
  background: linear-gradient(135deg, #075985, #0ea5e9);
  box-shadow: 0 4px 15px rgba(7, 89, 133, 0.3);
}

.countryCard:nth-child(6) .learnMoreBtn:hover {
  box-shadow: 0 8px 25px rgba(7, 89, 133, 0.5);
}

/* New Zealand Button */
.countryCard:nth-child(7) .learnMoreBtn {
  background: linear-gradient(135deg, #0d9488, #14b8a6);
  box-shadow: 0 4px 15px rgba(13, 148, 136, 0.3);
}

.countryCard:nth-child(7) .learnMoreBtn:hover {
  box-shadow: 0 8px 25px rgba(13, 148, 136, 0.5);
}

/* Europe Card Special Styling */
.europeCard {
  grid-column: span 2;
  width: 800px;
  max-width: 800px;
  min-width: 700px;
}

.europeanFlags {
  display: flex;
  justify-content: center;
  gap: 10px;
  margin: 20px 0;
  flex-wrap: wrap;
}

.smallFlag {
  width: 40px;
  height: 25px;
  object-fit: cover;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.featuredCountries {
  margin: 20px 0;
  text-align: center;
}

.featuredCountries h4 {
  color: #f1f5f9;
  margin-bottom: 10px;
  font-size: 1.1rem;
}

.featuredCountries ul {
  list-style: none;
  padding: 0;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 5px;
}

.featuredCountries li {
  color: #cbd5e1;
  font-size: 0.9rem;
}

.europeDetails {
  color: #94a3b8;
  text-align: center;
  margin-bottom: 20px;
  font-size: 0.95rem;
}

/* CTA Section */
.ctaSection {
  background: linear-gradient(145deg, rgba(59, 130, 246, 0.1), rgba(139, 92, 246, 0.1));
  backdrop-filter: blur(20px);
  padding: 50px;
  border-radius: 30px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  text-align: center;
}

.ctaTitle {
  font-size: 2.5rem;
  color: #f1f5f9;
  font-weight: 800;
  margin-bottom: 10px;
}

.ctaSubtitle {
  font-size: 1.8rem;
  color: #60a5fa;
  font-weight: 700;
  margin-bottom: 20px;
}

.ctaDescription {
  font-size: 1.1rem;
  color: #cbd5e1;
  margin-bottom: 30px;
  line-height: 1.6;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.ctaFeatures {
  display: flex;
  justify-content: center;
  gap: 30px;
  margin-bottom: 40px;
  flex-wrap: wrap;
}

.ctaFeature {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #22c55e;
  font-weight: 600;
}

.ctaFeature i {
  font-size: 1.2rem;
}

.scheduleBtn {
  background: linear-gradient(135deg, #22c55e, #16a34a);
  color: white;
  border: none;
  padding: 18px 40px;
  border-radius: 50px;
  font-size: 1.2rem;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 15px;
}

.scheduleBtn:hover {
  transform: translateY(-3px);
  box-shadow: 0 15px 35px rgba(34, 197, 94, 0.4);
}

.ctaNote {
  color: #94a3b8;
  font-size: 0.9rem;
  margin: 0;
}

/* Animations */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

@keyframes gradientShift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 4px 15px rgba(30, 58, 138, 0.4);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 6px 20px rgba(30, 58, 138, 0.6);
  }
}

.fadeInUp {
  animation: fadeInUp 1s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Design */

/* Extra Large Screens (1440px and up) */
@media (min-width: 1440px) {
  .globalEducationSection {
    padding: 140px 0;
  }

  .container {
    max-width: 1400px;
  }

  .mainTitle {
    font-size: 4rem;
  }

  .subtitle {
    font-size: 2.2rem;
  }

  .tagline {
    font-size: 1.6rem;
  }

  .statsSection {
    gap: 80px;
    margin-bottom: 120px;
  }

  .statItem {
    min-width: 220px;
    padding: 40px 35px;
  }

  .statNumber {
    font-size: 3.5rem;
  }

  .countriesGrid {
    column-gap: 60px;
    row-gap: 25px;
  }
}

/* Large Screens (1200px - 1439px) */
@media (min-width: 1200px) and (max-width: 1439px) {
  .globalEducationSection {
    padding: 130px 0;
  }

  .mainTitle {
    font-size: 3.8rem;
  }

  .statsSection {
    gap: 70px;
  }

  .statItem {
    min-width: 200px;
    padding: 35px 30px;
  }
}

/* Desktop (1024px - 1199px) */
@media (min-width: 1024px) and (max-width: 1199px) {
  .mainTitle {
    font-size: 3.2rem;
  }

  .countriesGrid {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    padding: 0 30px;
    column-gap: 90px;
    row-gap: 30px;
  }
  
  .countryCard {
    flex: 0 0 auto;
    width: 400px;
    max-width: 400px;
    min-height: 400px;
  }

  .europeCard {
    width: 800px;
    max-width: 800px;
  }

  .statsSection {
    gap: 50px;
  }

  .statItem {
    min-width: 180px;
    padding: 32px 28px;
  }
}

/* Large Desktop (1200px - 1599px) */
@media (min-width: 1200px) and (max-width: 1599px) {
  .countriesGrid {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    max-width: 1500px;
    margin: 0 auto 80px auto;
    column-gap: 90px;
    row-gap: 30px;
    padding: 0 40px;
  }
  
  .countryCard {
    flex: 0 0 auto;
    width: 400px;
    max-width: 400px;
    min-height: 420px;
  }
  
  /* Europe card is wider to accommodate more content */
  .europeCard {
    width: 800px;
    max-width: 800px;
  }
}

/* Extra Large Desktop (1600px and above) */
@media (min-width: 1600px) {
  .countriesGrid {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    max-width: 1800px;
    margin: 0 auto 80px auto;
    column-gap: 120px;
    row-gap: 40px;
    padding: 0 60px;
  }
  
  .countryCard {
    flex: 0 0 auto;
    width: 400px;
    max-width: 400px;
    min-height: 440px;
  }
  
  /* Europe card is wider to accommodate more content */
  .europeCard {
    width: 800px;
    max-width: 800px;
  }
}

/* Large Tablets and Small Desktops (900px - 1023px) */
@media (min-width: 900px) and (max-width: 1023px) {
  .countriesGrid {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    column-gap: 90px;
    row-gap: 30px;
    padding: 0 30px;
  }
  
  .countryCard {
    flex: 0 0 auto;
    width: 470px;
    max-width: 470px;
    min-width: 400px;
  }
  
  .europeCard {
    width: 470px;
    max-width: 470px;
  }
}

/* Medium Tablets (768px - 899px) */
@media (min-width: 768px) and (max-width: 899px) {
  .countriesGrid {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    column-gap: 75px;
    row-gap: 25px;
    padding: 0 25px;
  }

  .countryCard {
    flex: 0 0 auto;
    padding: 25px;
  }
}

/* Tablet Landscape (768px - 899px) */
@media (min-width: 768px) and (max-width: 899px) {
  .globalEducationSection {
    padding: 100px 0;
  }

  .mainTitle {
    font-size: 3rem;
  }

  .subtitle {
    font-size: 1.8rem;
  }

  .tagline {
    font-size: 1.3rem;
  }

  .description {
    font-size: 1.15rem;
    max-width: 700px;
  }

  .statsSection {
    gap: 40px;
    margin-bottom: 80px;
  }

  .statItem {
    min-width: 160px;
    padding: 28px 24px;
  }

  .statNumber {
    font-size: 2.8rem;
  }

  .countriesGrid {
    grid-template-columns: repeat(2, 1fr);
    gap: 25px;
    padding: 0 30px;
  }

  .countryCard {
    padding: 28px;
    min-height: 400px;
  }

  .countryName {
    font-size: 1.7rem;
    margin-bottom: 12px;
  }

  .universityCount {
    font-size: 1.05rem;
    margin-bottom: 18px;
    padding: 10px 18px;
  }

  .featuresList li {
    font-size: 0.95rem;
    margin-bottom: 8px;
  }

  .flagImage {
    width: 70px;
    height: 45px;
  }

  .europeCard {
    grid-column: span 1;
  }
}

/* Tablet Portrait (600px - 767px) */
@media (min-width: 600px) and (max-width: 767px) {
  .globalEducationSection {
    padding: 90px 0;
  }

  .container {
    padding: 0 30px;
  }

  .mainTitle {
    font-size: 2.8rem;
    line-height: 1.1;
  }

  .subtitle {
    font-size: 1.7rem;
  }

  .tagline {
    font-size: 1.25rem;
  }

  .description {
    font-size: 1.1rem;
    max-width: 600px;
  }

  .statsSection {
    gap: 30px;
    margin-bottom: 70px;
  }

  .statItem {
    min-width: 140px;
    padding: 24px 20px;
  }

  .statNumber {
    font-size: 2.6rem;
  }

  .countriesGrid {
    grid-template-columns: 1fr;
    gap: 20px;
    padding: 0 25px;
  }

  .countryCard {
    padding: 24px;
    min-height: 380px;
  }

  .countryName {
    font-size: 1.6rem;
    margin-bottom: 10px;
  }

  .universityCount {
    font-size: 1rem;
    margin-bottom: 16px;
    padding: 8px 14px;
  }

  .featuresList li {
    font-size: 0.9rem;
    margin-bottom: 7px;
  }

  .flagImage {
    width: 65px;
    height: 42px;
  }

  .smallFlag {
    width: 30px;
    height: 20px;
  }

  .learnMoreBtn {
    padding: 12px 24px;
    font-size: 0.95rem;
  }

  .ctaSection {
    padding: 45px 35px;
  }

  .ctaTitle {
    font-size: 2.2rem;
  }

  .ctaSubtitle {
    font-size: 1.6rem;
  }

  .ctaFeatures {
    gap: 20px;
  }

  .scheduleBtn {
    padding: 16px 35px;
    font-size: 1.15rem;
  }
}

/* Mobile Landscape (480px - 599px) */
@media (min-width: 480px) and (max-width: 599px) {
  .globalEducationSection {
    padding: 80px 0;
  }

  .container {
    padding: 0 25px;
  }

  .mainTitle {
    font-size: 2.5rem;
    line-height: 1.05;
    letter-spacing: -0.5px;
    margin-bottom: 18px;
    padding: 0 10px;
  }

  .subtitle {
    font-size: 1.6rem;
    margin-bottom: 8px;
    padding: 0 15px;
  }

  .tagline {
    font-size: 1.2rem;
    margin-bottom: 25px;
    padding: 0 15px;
  }

  .description {
    font-size: 1.1rem;
    max-width: 500px;
    padding: 0 20px;
    line-height: 1.6;
  }

  .statsSection {
    gap: 25px;
    margin-bottom: 60px;
    flex-wrap: wrap;
    justify-content: center;
  }

  .statItem {
    min-width: 130px;
    max-width: 180px;
    padding: 22px 18px;
    flex: 0 0 calc(50% - 12.5px);
  }

  .statNumber {
    font-size: 2.4rem;
  }

  .statLabel {
    font-size: 0.9rem;
  }

  .countriesGrid {
    grid-template-columns: 1fr;
    gap: 18px;
    padding: 0 20px;
  }

  .countryCard {
    padding: 22px;
    min-height: 350px;
  }

  .countryName {
    font-size: 1.6rem;
    margin-bottom: 10px;
  }

  .universityCount {
    font-size: 1rem;
    margin-bottom: 16px;
    padding: 8px 14px;
  }

  .featuresList li {
    font-size: 0.9rem;
    margin-bottom: 7px;
  }

  .flagImage {
    width: 60px;
    height: 38px;
  }

  .smallFlag {
    width: 28px;
    height: 18px;
  }

  .learnMoreBtn {
    padding: 12px 22px;
    font-size: 0.9rem;
  }

  .ctaSection {
    padding: 40px 30px;
  }

  .ctaTitle {
    font-size: 2rem;
  }

  .ctaSubtitle {
    font-size: 1.5rem;
  }

  .ctaFeatures {
    flex-direction: column;
    gap: 15px;
  }

  .scheduleBtn {
    padding: 15px 32px;
    font-size: 1.1rem;
  }
}

/* Mobile Portrait (320px - 479px) */
@media (min-width: 320px) and (max-width: 479px) {
  .globalEducationSection {
    padding: 60px 0;
  }

  .container {
    padding: 0 15px;
  }

  .headerSection {
    margin-bottom: 50px;
  }

  .mainTitle {
    font-size: 2rem;
    line-height: 1.05;
    margin-bottom: 15px;
    letter-spacing: -0.3px;
    padding: 0 5px;
    word-break: break-word;
  }

  .subtitle {
    font-size: 1.4rem;
    margin-bottom: 8px;
    padding: 0 5px;
    line-height: 1.2;
  }

  .tagline {
    font-size: 1.1rem;
    margin-bottom: 20px;
    padding: 0 5px;
    line-height: 1.3;
  }

  .description {
    font-size: 1rem;
    line-height: 1.6;
    max-width: 100%;
    padding: 0 10px;
  }

  .statsSection {
    flex-direction: column;
    align-items: center;
    gap: 15px;
    margin-bottom: 50px;
  }

  .statItem {
    width: 100%;
    max-width: 220px;
    padding: 20px 16px;
  }

  .statNumber {
    font-size: 2.2rem;
  }

  .statLabel {
    font-size: 0.85rem;
  }

  .countriesGrid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .countryCard {
    padding: 20px;
  }

  .countryName {
    font-size: 1.5rem;
    margin-bottom: 8px;
  }

  .universityCount {
    font-size: 0.95rem;
    margin-bottom: 16px;
  }

  .featuresList {
    margin-bottom: 20px;
  }

  .featuresList li {
    font-size: 0.9rem;
    margin-bottom: 6px;
  }

  .flagImage {
    width: 60px;
    height: 40px;
  }

  .smallFlag {
    width: 28px;
    height: 18px;
  }

  .europeanFlags {
    gap: 8px;
    margin-bottom: 15px;
  }

  .featuredCountries h4 {
    font-size: 1rem;
    margin-bottom: 8px;
  }

  .featuredCountries ul li {
    font-size: 0.85rem;
  }

  .learnMoreBtn {
    padding: 12px 20px;
    font-size: 0.9rem;
  }

  .ctaSection {
    padding: 35px 20px;
  }

  .ctaTitle {
    font-size: 1.8rem;
    margin-bottom: 8px;
  }

  .ctaSubtitle {
    font-size: 1.3rem;
    margin-bottom: 20px;
  }

  .ctaFeatures {
    flex-direction: column;
    gap: 12px;
    margin-bottom: 30px;
  }

  .ctaFeature {
    font-size: 0.9rem;
  }

  .scheduleBtn {
    padding: 14px 28px;
    font-size: 1rem;
    width: 100%;
    max-width: 280px;
  }

  .ctaNote {
    font-size: 0.85rem;
  }
}

/* Very Small Screens (below 320px) */
@media (max-width: 319px) {
  .globalEducationSection {
    padding: 50px 0;
  }

  .container {
    padding: 0 15px;
  }

  .mainTitle {
    font-size: 1.8rem;
    line-height: 1.05;
    letter-spacing: -0.2px;
    margin-bottom: 12px;
    padding: 0 10px;
    word-break: break-word;
  }

  .subtitle {
    font-size: 1.2rem;
    margin-bottom: 6px;
    padding: 0 15px;
    line-height: 1.2;
  }

  .tagline {
    font-size: 1rem;
    margin-bottom: 18px;
    padding: 0 15px;
    line-height: 1.3;
  }

  .description {
    font-size: 0.95rem;
    padding: 0 20px;
    line-height: 1.5;
  }

  .statItem {
    max-width: 200px;
    padding: 18px 14px;
  }

  .statNumber {
    font-size: 2rem;
  }

  .countryCard {
    padding: 18px;
  }

  .countryName {
    font-size: 1.3rem;
  }

  .flagImage {
    width: 50px;
    height: 32px;
  }

  .smallFlag {
    width: 24px;
    height: 16px;
  }

  .ctaSection {
    padding: 30px 15px;
  }

  .ctaTitle {
    font-size: 1.6rem;
  }

  .ctaSubtitle {
    font-size: 1.2rem;
  }

  .scheduleBtn {
    padding: 12px 24px;
    font-size: 0.95rem;
  }
}

/* Additional Responsive Enhancements */

/* Touch Device Optimizations */
@media (hover: none) and (pointer: coarse) {
  .countryCard:hover {
    transform: none;
  }

  .countryCard:active {
    transform: scale(0.98);
  }

  .statItem:hover {
    transform: none;
  }

  .statItem:active {
    transform: scale(0.98);
  }

  .learnMoreBtn:hover {
    transform: none;
  }

  .learnMoreBtn:active {
    transform: scale(0.95);
  }

  .scheduleBtn:hover {
    transform: none;
  }

  .scheduleBtn:active {
    transform: scale(0.95);
  }
}

/* High DPI Displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .flagImage,
  .smallFlag {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

/* Landscape Orientation on Mobile */
@media (max-height: 500px) and (orientation: landscape) {
  .globalEducationSection {
    padding: 40px 0;
  }

  .headerSection {
    margin-bottom: 30px;
  }

  .mainTitle {
    font-size: 2rem;
    margin-bottom: 10px;
  }

  .subtitle {
    font-size: 1.3rem;
    margin-bottom: 5px;
  }

  .tagline {
    font-size: 1rem;
    margin-bottom: 15px;
  }

  .description {
    font-size: 0.95rem;
    margin-bottom: 20px;
  }

  .statsSection {
    flex-direction: row;
    gap: 20px;
    margin-bottom: 40px;
    flex-wrap: wrap;
    justify-content: center;
  }

  .statItem {
    min-width: 120px;
    max-width: 150px;
    padding: 15px 12px;
  }

  .statNumber {
    font-size: 1.8rem;
  }

  .statLabel {
    font-size: 0.75rem;
  }

  .countriesGrid {
    gap: 15px;
  }

  .countryCard {
    padding: 15px;
  }

  .ctaSection {
    padding: 25px 20px;
  }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  .gradientOrb1,
  .gradientOrb2,
  .gradientOrb3 {
    animation: none;
  }

  .mainTitle {
    animation: none;
  }

  .countryCard,
  .statItem,
  .learnMoreBtn,
  .scheduleBtn {
    transition: none;
  }

  .countryCard:hover,
  .statItem:hover {
    transform: none;
  }
}

/* Print Styles */
@media print {
  .globalEducationSection {
    background: white !important;
    color: black !important;
    padding: 20px 0;
  }

  .backgroundElements {
    display: none;
  }

  .mainTitle,
  .subtitle,
  .tagline,
  .description,
  .countryName,
  .statNumber,
  .statLabel {
    color: black !important;
    background: none !important;
    -webkit-text-fill-color: initial !important;
  }

  .countryCard,
  .statItem {
    background: white !important;
    border: 1px solid #ccc !important;
    box-shadow: none !important;
  }

  .scheduleBtn,
  .learnMoreBtn {
    background: white !important;
    color: black !important;
    border: 2px solid black !important;
  }

  .ctaSection {
    background: white !important;
  }
}

/* Focus Management and Accessibility */
.learnMoreBtn:focus,
.scheduleBtn:focus {
  outline: 3px solid rgba(59, 130, 246, 0.6);
  outline-offset: 2px;
}

@supports selector(:focus-visible) {
  .learnMoreBtn:focus,
  .scheduleBtn:focus {
    outline: none;
  }

  .learnMoreBtn:focus-visible,
  .scheduleBtn:focus-visible {
    outline: 3px solid rgba(59, 130, 246, 0.6);
    outline-offset: 2px;
  }
}

/* Ensure minimum touch targets on mobile */
@media (max-width: 768px) {
  .learnMoreBtn,
  .scheduleBtn {
    min-height: 44px;
    min-width: 44px;
  }

  /* Improve text readability on mobile */
  .mainTitle,
  .subtitle,
  .tagline,
  .description {
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  }

  /* Better spacing for mobile header */
  .headerSection {
    padding: 0 5px;
  }
}

/* FORCE SINGLE COLUMN FOR ALL MOBILE DEVICES */
@media (max-width: 767px) {
  .countriesGrid {
    grid-template-columns: 1fr !important;
    gap: 20px;
    padding: 0 20px;
    justify-items: center;
  }

  .countryCard {
    width: 100% !important;
    max-width: 100% !important;
  }
}

/* Very Small Mobile Devices (below 480px) */
@media (max-width: 479px) {
  .countriesGrid {
    grid-template-columns: 1fr !important;
    gap: 16px;
    padding: 0 15px;
    justify-items: center;
  }

  .countryCard {
    width: 100% !important;
    max-width: 100% !important;
    padding: 20px;
  }
}

/* Extra Small Mobile (below 375px) */
@media (max-width: 374px) {
  .countriesGrid {
    grid-template-columns: 1fr !important;
    gap: 12px;
    padding: 0 10px;
    justify-items: center;
  }

  .countryCard {
    width: 100% !important;
    max-width: 100% !important;
    padding: 18px;
  }
}

/* Additional responsive typography improvements */
@media (max-width: 640px) {
  .container {
    padding: 0 12px;
  }

  .mainTitle {
    font-size: 2.2rem;
    line-height: 1.05;
    letter-spacing: -0.4px;
    margin-bottom: 16px;
    padding: 0 5px;
  }

  .subtitle {
    font-size: 1.5rem;
    margin-bottom: 8px;
    padding: 0 5px;
  }

  .tagline {
    font-size: 1.15rem;
    margin-bottom: 22px;
    padding: 0 5px;
  }

  .description {
    font-size: 1.05rem;
    line-height: 1.65;
    padding: 0 8px;
  }

  /* Country cards for medium mobile */
  .countriesGrid {
    grid-template-columns: 1fr;
    gap: 16px;
    padding: 0 20px;
  }

  .countryCard {
    padding: 20px;
    min-height: 340px;
  }

  .countryName {
    font-size: 1.5rem;
    margin-bottom: 8px;
  }

  .universityCount {
    font-size: 0.95rem;
    margin-bottom: 15px;
    padding: 7px 12px;
  }

  .featuresList li {
    font-size: 0.85rem;
    margin-bottom: 6px;
  }

  .flagImage {
    width: 55px;
    height: 35px;
  }

  .smallFlag {
    width: 26px;
    height: 17px;
  }

  .learnMoreBtn {
    padding: 11px 20px;
    font-size: 0.85rem;
  }
}

/* Ultra-wide screen optimizations */
@media (min-width: 1920px) {
  .mainTitle {
    font-size: 4.5rem;
    letter-spacing: -1.5px;
  }

  .subtitle {
    font-size: 2.5rem;
  }

  .tagline {
    font-size: 1.8rem;
  }

  .description {
    font-size: 1.4rem;
    max-width: 900px;
  }

  .headerSection {
    margin-bottom: 80px;
  }
}

/* Container queries support (future-proofing) */
@container (max-width: 400px) {
  .countriesGrid {
    grid-template-columns: 1fr;
  }

  .statItem {
    min-width: 100%;
  }

  .mainTitle {
    font-size: 1.6rem;
    line-height: 1.1;
  }

  .subtitle {
    font-size: 1.1rem;
  }

  .tagline {
    font-size: 0.95rem;
  }
}

/* FINAL MOBILE OVERRIDE - ABSOLUTELY FORCE SINGLE COLUMN */
@media screen and (max-width: 767px) {
  .countriesGrid {
    display: grid !important;
    grid-template-columns: 1fr !important;
    grid-template-rows: auto !important;
  }

  .countryCard {
    width: 100% !important;
    max-width: none !important;
    grid-column: 1 !important;
  }
}

@media screen and (max-width: 480px) {
  .countriesGrid {
    display: grid !important;
    grid-template-columns: 1fr !important;
    grid-template-rows: auto !important;
  }

  .countryCard {
    width: 100% !important;
    max-width: none !important;
    grid-column: 1 !important;
  }
}

/* Text overflow handling for very long titles */
@media (max-width: 480px) {
  .container {
    padding: 0 10px;
  }

  .mainTitle {
    overflow-wrap: break-word;
    word-wrap: break-word;
    hyphens: auto;
    -webkit-hyphens: auto;
    -ms-hyphens: auto;
    padding: 0 2px;
    font-size: 1.9rem;
  }

  .subtitle {
    padding: 0 2px;
    font-size: 1.3rem;
  }

  .tagline {
    padding: 0 2px;
    font-size: 1.05rem;
  }

  .description {
    padding: 0 5px;
    font-size: 0.98rem;
  }

  /* Enhanced country card responsiveness for small mobile */
  .countryCard {
    padding: 18px;
    min-height: 320px;
  }

  .countryName {
    font-size: 1.4rem;
    margin-bottom: 8px;
    line-height: 1.2;
  }

  .universityCount {
    font-size: 0.9rem;
    margin-bottom: 14px;
    padding: 6px 10px;
  }

  .featuresList {
    margin-bottom: 18px;
  }

  .featuresList li {
    font-size: 0.8rem;
    margin-bottom: 5px;
    line-height: 1.3;
  }

  .flagImage {
    width: 50px;
    height: 32px;
  }

  .smallFlag {
    width: 24px;
    height: 16px;
  }

  .europeanFlags {
    gap: 6px;
    margin-bottom: 12px;
    flex-wrap: wrap;
    justify-content: center;
  }

  .featuredCountries h4 {
    font-size: 0.95rem;
    margin-bottom: 6px;
  }

  .featuredCountries ul li {
    font-size: 0.8rem;
    margin-bottom: 3px;
  }

  .learnMoreBtn {
    padding: 10px 18px;
    font-size: 0.8rem;
    min-height: 44px;
  }
}

/* Improved gradient text fallback for older browsers */
@supports not (-webkit-background-clip: text) {
  .mainTitle {
    background: none !important;
    color: #f1f5f9 !important;
    -webkit-text-fill-color: initial !important;
  }
}
