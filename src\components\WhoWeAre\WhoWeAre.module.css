/* Who We Are Component Styles */

.whoWeAreSection {
  padding: 80px 0;
  background: linear-gradient(135deg, #0a0f1c 0%, #1a1f2e 25%, #2d1b69 50%, #1e1b4b 75%, #0f172a 100%);
  position: relative;
  overflow: hidden;
  min-height: 100vh;
}

/* Enhanced Background Effects */
.backgroundEffects {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
  pointer-events: none;
}

.whoWeAreSection::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    radial-gradient(circle at 20% 20%, rgba(139, 92, 246, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(59, 130, 246, 0.12) 0%, transparent 50%),
    radial-gradient(circle at 40% 60%, rgba(236, 72, 153, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 70% 30%, rgba(34, 197, 94, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 10% 80%, rgba(251, 191, 36, 0.06) 0%, transparent 50%);
  background-size: 600px 600px, 500px 500px, 400px 400px, 450px 450px, 350px 350px;
  animation: backgroundPulse 25s ease-in-out infinite;
  z-index: 1;
}

/* Floating Orbs */
.floatingOrb {
  position: absolute;
  border-radius: 50%;
  animation: floatOrb 20s ease-in-out infinite;
}

.floatingOrb:nth-child(1) {
  width: 80px;
  height: 80px;
  top: 10%;
  left: 5%;
  background: radial-gradient(circle, rgba(139, 92, 246, 0.8) 0%, rgba(196, 181, 253, 0.4) 70%, transparent 100%);
  box-shadow: 0 0 40px rgba(139, 92, 246, 0.6);
  animation-delay: 0s;
  animation-duration: 22s;
}

.floatingOrb:nth-child(2) {
  width: 60px;
  height: 60px;
  top: 20%;
  right: 10%;
  background: radial-gradient(circle, rgba(59, 130, 246, 0.8) 0%, rgba(147, 197, 253, 0.4) 70%, transparent 100%);
  box-shadow: 0 0 35px rgba(59, 130, 246, 0.6);
  animation-delay: -5s;
  animation-duration: 25s;
}

.floatingOrb:nth-child(3) {
  width: 100px;
  height: 100px;
  bottom: 15%;
  left: 8%;
  background: radial-gradient(circle, rgba(236, 72, 153, 0.8) 0%, rgba(251, 207, 232, 0.4) 70%, transparent 100%);
  box-shadow: 0 0 50px rgba(236, 72, 153, 0.6);
  animation-delay: -10s;
  animation-duration: 28s;
}

.floatingOrb:nth-child(4) {
  width: 70px;
  height: 70px;
  top: 60%;
  right: 15%;
  background: radial-gradient(circle, rgba(34, 197, 94, 0.8) 0%, rgba(134, 239, 172, 0.4) 70%, transparent 100%);
  box-shadow: 0 0 40px rgba(34, 197, 94, 0.6);
  animation-delay: -15s;
  animation-duration: 24s;
}

.floatingOrb:nth-child(5) {
  width: 90px;
  height: 90px;
  top: 40%;
  left: 15%;
  background: radial-gradient(circle, rgba(251, 191, 36, 0.8) 0%, rgba(254, 240, 138, 0.4) 70%, transparent 100%);
  box-shadow: 0 0 45px rgba(251, 191, 36, 0.6);
  animation-delay: -20s;
  animation-duration: 26s;
}

.floatingOrb:nth-child(6) {
  width: 50px;
  height: 50px;
  bottom: 30%;
  right: 25%;
  background: radial-gradient(circle, rgba(168, 85, 247, 0.8) 0%, rgba(221, 214, 254, 0.4) 70%, transparent 100%);
  box-shadow: 0 0 30px rgba(168, 85, 247, 0.6);
  animation-delay: -25s;
  animation-duration: 20s;
}

/* Geometric Shapes */
.geometricShapes {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.shape1 {
  position: absolute;
  top: 15%;
  left: 20%;
  width: 40px;
  height: 40px;
  background: linear-gradient(45deg, rgba(139, 92, 246, 0.3), transparent);
  transform: rotate(45deg);
  animation: rotateShape 15s linear infinite;
}

.shape2 {
  position: absolute;
  top: 70%;
  right: 20%;
  width: 60px;
  height: 60px;
  background: linear-gradient(45deg, rgba(59, 130, 246, 0.3), transparent);
  border-radius: 50%;
  animation: scaleShape 12s ease-in-out infinite;
}

.shape3 {
  position: absolute;
  bottom: 20%;
  left: 30%;
  width: 0;
  height: 0;
  border-left: 25px solid transparent;
  border-right: 25px solid transparent;
  border-bottom: 43px solid rgba(236, 72, 153, 0.3);
  animation: floatShape 18s ease-in-out infinite;
}

.shape4 {
  position: absolute;
  top: 50%;
  right: 10%;
  width: 35px;
  height: 35px;
  background: rgba(34, 197, 94, 0.3);
  animation: rotateShape 20s linear infinite reverse;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 15px;
  position: relative;
  z-index: 2;
}

/* Section Header */
.sectionHeader {
  text-align: center;
  margin-bottom: 80px;
  animation: fadeInUp 1s ease-out;
}

.headerBadge {
  display: inline-flex;
  align-items: center;
  gap: 12px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.08));
  color: #a78bfa;
  padding: 12px 30px;
  border-radius: 30px;
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 25px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
  text-transform: uppercase;
  letter-spacing: 1px;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.headerBadge i {
  font-size: 1.2rem;
  animation: usersPulse 3s ease-in-out infinite;
  color: #60a5fa;
}

.sectionTitle {
  font-size: 4rem;
  font-weight: 800;
  background: linear-gradient(135deg, #ffffff 0%, #e2e8f0 30%, #ffffff 60%, #f1f5f9 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  line-height: 1.2;
  margin-bottom: 30px;
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.4);
}

.titleHighlight {
  background: linear-gradient(135deg, #a78bfa 0%, #8b5cf6 50%, #7c3aed 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.sectionDescription {
  font-size: 1.3rem;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.8;
  max-width: 800px;
  margin: 0 auto;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Stats Container */
.statsContainer {
  position: relative;
  margin-bottom: 60px;
}

.statsGrid {
  display: flex;
  justify-content: center;
  gap: 40px;
  flex-wrap: wrap;
}

.statCard {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.08));
  padding: 40px 30px;
  border-radius: 25px;
  text-align: center;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(30px);
  border: 2px solid rgba(255, 255, 255, 0.1);
  box-shadow: 
    0 25px 80px rgba(0, 0, 0, 0.5),
    0 10px 40px rgba(139, 92, 246, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  animation: statCardEntrance 0.8s ease-out var(--delay) both;
  min-width: 200px;
  flex: 1;
  max-width: 280px;
}

.statCard::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #a78bfa, #8b5cf6, #7c3aed);
  box-shadow: 0 0 20px rgba(167, 139, 250, 0.5);
}

.statCard:hover {
  transform: translateY(-15px) scale(1.03);
  box-shadow: 
    0 35px 100px rgba(139, 92, 246, 0.4),
    0 15px 50px rgba(0, 0, 0, 0.4),
    inset 0 2px 0 rgba(255, 255, 255, 0.3);
  border-color: rgba(167, 139, 250, 0.3);
}

.statCard:hover .statGlow {
  opacity: 1;
}

.statIcon {
  width: 70px;
  height: 70px;
  background: linear-gradient(135deg, #a78bfa, #8b5cf6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.8rem;
  margin: 0 auto 25px;
  box-shadow: 
    0 15px 40px rgba(167, 139, 250, 0.4),
    0 5px 15px rgba(0, 0, 0, 0.3);
  animation: iconFloat 3s ease-in-out infinite;
  position: relative;
  z-index: 2;
}

.statContent {
  position: relative;
  z-index: 2;
}

.statNumber {
  font-size: 3.5rem;
  font-weight: 800;
  background: linear-gradient(135deg, #a78bfa 0%, #8b5cf6 50%, #7c3aed 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  line-height: 1;
  margin-bottom: 10px;
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  animation: numberPulse 4s ease-in-out infinite;
}

.statLabel {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.statGlow {
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(167, 139, 250, 0.1) 0%, transparent 70%);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.statShimmer {
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.05), transparent);
  transform: rotate(45deg);
  animation: statShimmer 5s ease-in-out infinite;
  pointer-events: none;
}



/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes backgroundPulse {
  0%, 100% {
    transform: scale(1);
    opacity: 0.8;
  }
  25% {
    transform: scale(1.05);
    opacity: 1;
  }
  50% {
    transform: scale(0.95);
    opacity: 0.6;
  }
  75% {
    transform: scale(1.02);
    opacity: 0.9;
  }
}

@keyframes floatOrb {
  0%, 100% {
    transform: translateY(0px) translateX(0px) scale(1);
  }
  25% {
    transform: translateY(-30px) translateX(20px) scale(1.1);
  }
  50% {
    transform: translateY(-10px) translateX(-15px) scale(0.9);
  }
  75% {
    transform: translateY(-40px) translateX(10px) scale(1.05);
  }
}

@keyframes rotateShape {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes scaleShape {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
}

@keyframes floatShape {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes usersPulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

@keyframes statCardEntrance {
  from {
    opacity: 0;
    transform: translateY(50px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes iconFloat {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-8px);
  }
}

@keyframes numberPulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes statShimmer {
  0% {
    transform: translateX(-100%) translateY(-100%) rotate(45deg);
  }
  50% {
    transform: translateX(100%) translateY(100%) rotate(45deg);
  }
  100% {
    transform: translateX(200%) translateY(200%) rotate(45deg);
  }
}



/* Responsive Design */
@media (max-width: 768px) {
  .whoWeAreSection {
    padding: 50px 0;
    min-height: auto;
  }

  .sectionTitle {
    font-size: 2.8rem;
  }

  .sectionDescription {
    font-size: 1.1rem;
  }

  .statsGrid {
    flex-direction: column;
    gap: 25px;
    align-items: center;
  }

  .statCard {
    max-width: 100%;
    width: 100%;
    max-width: 350px;
  }



  .floatingOrb {
    display: none;
  }

  .geometricShapes {
    display: none;
  }
}
