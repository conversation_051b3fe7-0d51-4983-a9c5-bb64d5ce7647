/* Featured Universities Section */
.featuredSection {
  position: relative;
  padding: 6rem 0;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 35%, #334155 70%, #475569 100%);
  overflow: hidden;
}

/* Container */
.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 1rem;
}

@media (min-width: 1024px) {
  .container {
    padding: 0 2rem;
  }
}

/* Section Header */
.sectionHeader {
  text-align: center;
  margin-bottom: 4rem;
  animation: fadeInUp 0.8s ease-out;
}

.sectionTitle {
  font-size: 3rem;
  font-weight: 800;
  background: linear-gradient(135deg, #e2e8f0, #3b82f6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 1rem;
  line-height: 1.2;
}

@media (min-width: 768px) {
  .sectionTitle {
    font-size: 4rem;
  }
}

.sectionDescription {
  font-size: 1.2rem;
  color: #cbd5e1;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

/* Universities Grid */
.universitiesGrid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 2rem;
}

@media (min-width: 768px) {
  .universitiesGrid {
    grid-template-columns: repeat(2, 1fr);
    gap: 2.5rem;
  }
}

@media (min-width: 1200px) {
  .universitiesGrid {
    grid-template-columns: repeat(3, 1fr);
    gap: 3rem;
  }
}

/* University Card */
.universityCard {
  position: relative;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid rgba(59, 130, 246, 0.2);
  animation: fadeInUp 0.8s ease-out both;
}

.universityCard:hover {
  transform: translateY(-10px);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.4);
  border-color: rgba(59, 130, 246, 0.5);
  background: rgba(255, 255, 255, 0.08);
}

/* Badge */
.badge {
  position: absolute;
  top: 1rem;
  right: 1rem;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  z-index: 10;
  backdrop-filter: blur(10px);
}

.featured {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  color: white;
}

.popular {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
}

.topRated {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
}

.elite {
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
  color: white;
}

.research {
  background: linear-gradient(135deg, #06b6d4, #0891b2);
  color: white;
}

.innovative {
  background: linear-gradient(135deg, #f97316, #ea580c);
  color: white;
}

/* Image Container */
.imageContainer {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.universityImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.4s ease;
}

.universityCard:hover .universityImage {
  transform: scale(1.1);
}

.imageOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to bottom, transparent 0%, rgba(0, 0, 0, 0.1) 100%);
}

/* Card Content */
.cardContent {
  padding: 1.5rem;
}

.universityName {
  font-size: 1.25rem;
  font-weight: 700;
  color: #f1f5f9;
  margin-bottom: 1rem;
  line-height: 1.3;
}

.locationInfo {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #cbd5e1;
  margin-bottom: 1.5rem;
  font-weight: 500;
}

.icon {
  font-size: 1rem;
  display: inline-block;
  width: 20px;
  text-align: center;
}

/* Stats Grid */
.statsGrid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
}

.statItem {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: #cbd5e1;
}

.statItem .icon {
  width: 20px;
  font-size: 1rem;
}

/* Learn More Button */
.learnMoreBtn {
  width: 100%;
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.learnMoreBtn:hover {
  background: linear-gradient(135deg, #2563eb, #1d4ed8);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
}

.arrow {
  transition: transform 0.3s ease;
  font-size: 1.2rem;
}

.learnMoreBtn:hover .arrow {
  transform: translateX(3px);
}

/* Background Elements */
.backgroundElements {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
  pointer-events: none;
}

.bgShape1,
.bgShape2,
.bgShape3 {
  position: absolute;
  border-radius: 50%;
  background: rgba(59, 130, 246, 0.05);
  animation: floatSlow 10s ease-in-out infinite;
}

.bgShape1 {
  width: 300px;
  height: 300px;
  top: 10%;
  left: -5%;
  animation-delay: 0s;
}

.bgShape2 {
  width: 200px;
  height: 200px;
  top: 50%;
  right: -3%;
  animation-delay: 3s;
}

.bgShape3 {
  width: 150px;
  height: 150px;
  bottom: 20%;
  left: 20%;
  animation-delay: 6s;
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes floatSlow {
  0%, 100% { 
    transform: translateY(0px) rotate(0deg); 
  }
  50% { 
    transform: translateY(-20px) rotate(180deg); 
  }
}

/* Mobile Adjustments */
@media (max-width: 768px) {
  .featuredSection {
    padding: 4rem 0;
  }
  
  .sectionHeader {
    margin-bottom: 3rem;
  }
  
  .sectionTitle {
    font-size: 2.5rem;
  }
  
  .universitiesGrid {
    gap: 1.5rem;
  }
}
