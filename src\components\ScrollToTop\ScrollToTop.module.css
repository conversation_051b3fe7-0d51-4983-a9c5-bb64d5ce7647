/* Sticky Container */
.scrollToTopContainer {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  z-index: 99999;
  pointer-events: none;
}

/* Global Scroll to Top Button */
.scrollToTop {
  position: relative;
  width: 3.5rem;
  height: 3.5rem;
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 50%, #1d4ed8 100%);
  color: white;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.3rem;
  box-shadow:
    0 8px 32px rgba(37, 99, 235, 0.4),
    0 4px 16px rgba(37, 99, 235, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  border: 2px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  overflow: hidden;
}

/* Visibility states */
.scrollToTopContainer:has(.visible) {
  pointer-events: auto;
}

.visible {
  opacity: 1;
  visibility: visible;
  transform: translate3d(0, 0, 0) scale(1);
  pointer-events: auto;
}

.hidden {
  opacity: 0;
  visibility: hidden;
  transform: translate3d(0, 20px, 0) scale(0.8);
  pointer-events: none;
}

.scrollToTop::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.2) 0%, 
    transparent 50%, 
    rgba(255, 255, 255, 0.1) 100%);
  border-radius: inherit;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.scrollToTop:hover {
  transform: translateY(-4px) scale(1.05);
  box-shadow:
    0 16px 48px rgba(37, 99, 235, 0.5),
    0 8px 24px rgba(37, 99, 235, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.2);
}

.scrollToTop:hover::before {
  opacity: 1;
}

.scrollToTop:active {
  transform: translateY(-2px) scale(1.02);
  transition: all 0.1s ease;
}

.scrollToTop.scrolling {
  transform: translateY(-2px) scale(1.02);
  box-shadow:
    0 12px 40px rgba(37, 99, 235, 0.45),
    0 6px 20px rgba(37, 99, 235, 0.25),
    inset 0 1px 0 rgba(255, 255, 255, 0.25);
}

/* Ripple effect */
.ripple {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.6s ease, height 0.6s ease;
  pointer-events: none;
}

.scrollToTop:active .ripple {
  width: 100px;
  height: 100px;
}

/* Icon styling */
.scrollToTop i {
  transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  font-size: 1.3rem;
  z-index: 2;
  position: relative;
}

.scrollToTop:hover i {
  transform: translateY(-2px);
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translate3d(0, 20px, 0) scale(0.8);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0) scale(1);
  }
}

@keyframes pulse {
  0%, 100% {
    box-shadow:
      0 8px 32px rgba(37, 99, 235, 0.4),
      0 4px 16px rgba(37, 99, 235, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
  }
  50% {
    box-shadow:
      0 12px 40px rgba(37, 99, 235, 0.5),
      0 6px 20px rgba(37, 99, 235, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.25);
  }
}

/* Subtle pulse animation when visible */
.scrollToTop {
  animation: fadeInUp 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94), 
             pulse 3s ease-in-out infinite 2s;
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .scrollToTopContainer {
    bottom: 1.5rem;
    right: 1.5rem;
  }

  .scrollToTop {
    width: 3rem;
    height: 3rem;
    font-size: 1.1rem;
  }

  .scrollToTop:hover {
    transform: translateY(-2px) scale(1.03);
  }
}

/* Small mobile optimizations */
@media (max-width: 480px) {
  .scrollToTopContainer {
    bottom: 1rem;
    right: 1rem;
  }

  .scrollToTop {
    width: 2.8rem;
    height: 2.8rem;
    font-size: 1rem;
  }
}

/* Large desktop optimizations */
@media (min-width: 1200px) {
  .scrollToTopContainer {
    bottom: 3rem;
    right: 3rem;
  }

  .scrollToTop {
    width: 4rem;
    height: 4rem;
    font-size: 1.4rem;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .scrollToTop {
    background: #0066cc;
    border: 3px solid #ffffff;
  }
  
  .scrollToTop:hover {
    background: #0052a3;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .scrollToTop {
    animation: none;
    transition: opacity 0.3s ease;
  }
  
  .scrollToTop:hover {
    transform: none;
  }
  
  .scrollToTop i {
    transition: none;
  }
  
  .scrollToTop:hover i {
    transform: none;
  }
}

/* Focus styles for accessibility */
.scrollToTop:focus {
  outline: 3px solid rgba(37, 99, 235, 0.6);
  outline-offset: 4px;
}

.scrollToTop:focus-visible {
  outline: 3px solid rgba(37, 99, 235, 0.8);
  outline-offset: 4px;
}

/* Dark mode optimizations */
@media (prefers-color-scheme: dark) {
  .scrollToTop {
    background: linear-gradient(135deg, #1e40af 0%, #1d4ed8 50%, #2563eb 100%);
    box-shadow:
      0 8px 32px rgba(30, 64, 175, 0.4),
      0 4px 16px rgba(30, 64, 175, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.15);
  }

  .scrollToTop:hover {
    box-shadow:
      0 16px 48px rgba(30, 64, 175, 0.5),
      0 8px 24px rgba(30, 64, 175, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
  }
}
